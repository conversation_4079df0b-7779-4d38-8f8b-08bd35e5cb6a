import request from '@/utils/request'

// 查询再保责任映射列表
export function listRiskLiabilityMapping(query) {
  return request({
    url: '/huida-reinsurance/reinsurance/riskLiability/mapping/list',
    method: 'get',
    params: query
  })
}

// 新增再保责任映射
export function addRiskLiabilityMapping(data) {
  return request({
    url: '/huida-reinsurance/reinsurance/riskLiability/mapping',
    method: 'post',
    data: data
  })
}

// 修改再保责任映射
export function updateRiskLiabilityMapping(data) {
  return request({
    url: '/huida-reinsurance/reinsurance/riskLiability/mapping',
    method: 'put',
    data: data
  })
}

// 删除再保责任映射
export function delRiskLiabilityMapping(id) {
  return request({
    url: '/huida-reinsurance/reinsurance/riskLiability/mapping/' + id,
    method: 'delete'
  })
}
