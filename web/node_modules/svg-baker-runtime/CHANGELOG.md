# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

<a name="1.4.7"></a>
## [1.4.7](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/compare/<EMAIL>-baker-runtime@1.4.7) (2020-04-28)


### Bug Fixes

* move `mask` & `clipPath` elements outside symbol ([2847f07](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/2847f07))




<a name="1.4.6"></a>
## [1.4.6](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/compare/<EMAIL>-baker-runtime@1.4.6) (2020-04-12)


### Bug Fixes

* refer to transpiled code for webpack ([b139b21](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/b139b21)), closes [#385](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/issues/385)




<a name="1.4.5"></a>
## [1.4.5](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/compare/<EMAIL>-baker-runtime@1.4.5) (2020-04-08)


### Bug Fixes

* add aria-hidden attribute to SVG sprites ([d9a2a11](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/d9a2a11)), closes [JetBrains/svg-sprite-loader#315](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/issues/315)
* dont reference browser globals on init ([aabcd04](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/aabcd04))
* replace all instances of urls in attribute ([39f8aaf](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/39f8aaf))
* **typecheck:** fixed wrong check for angular on window ([670a06b](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/670a06b))




<a name="1.4.4"></a>
## [1.4.4](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/compare/<EMAIL>-baker-runtime@1.4.4) (2020-04-01)


### Bug Fixes

* add aria-hidden attribute to SVG sprites ([d9a2a11](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/d9a2a11)), closes [JetBrains/svg-sprite-loader#315](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/issues/315)
* dont reference browser globals on init ([aabcd04](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/aabcd04))
* replace all instances of urls in attribute ([39f8aaf](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/39f8aaf))




<a name="1.4.3"></a>
## [1.4.3](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/compare/<EMAIL>-baker-runtime@1.4.3) (2020-01-24)


### Bug Fixes

* dont reference browser globals on init ([aabcd04](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/aabcd04))
* replace all instances of urls in attribute ([39f8aaf](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/39f8aaf))




<a name="1.4.2"></a>
## [1.4.2](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/compare/<EMAIL>-baker-runtime@1.4.2) (2019-04-27)




**Note:** Version bump only for package svg-baker-runtime

<a name="1.4.1"></a>
## [1.4.1](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/compare/<EMAIL>-baker-runtime@1.4.1) (2019-04-27)


### Bug Fixes

* dont reference browser globals on init ([aabcd04](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/aabcd04))
* replace all instances of urls in attribute ([39f8aaf](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/39f8aaf))




<a name="1.4.0"></a>
# [1.4.0](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/compare/<EMAIL>-baker-runtime@1.4.0) (2018-10-29)


### Bug Fixes

* **runtime:** apply styles in dynamically appended symbols in Edge ([52a5033](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/52a5033))
* **runtime:** fix attaching to existing DOM node ([631cc2a](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/631cc2a))
* update package info ([7cc1b95](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/7cc1b95))
* upgrade merge-options due to severity vulnerabilities ([0538c60](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/0538c60))


### Features

* **runtime:** add ability to attach sprite to existing DOM element ([6f92906](https://github.com/JetBrains/svg-mixer/tree/v1/packages/svg-baker-runtime/commit/6f92906))




<a name="1.2.95"></a>
## 1.2.95 (2017-05-13)


### Bug Fixes

* **browser-sprite:** refactor & don't throw an error when Angular location update event fires ([537dd81](https://github.com/kisenka/svg-baker/packages/svg-baker-runtime/commit/537dd81))
