{"name": "@vue/cli-plugin-vuex", "version": "4.5.19", "description": "Vuex plugin for vue-cli", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-cli.git", "directory": "packages/@vue/cli-plugin-vuex"}, "keywords": ["vue", "cli", "vuex"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-cli/issues"}, "homepage": "https://github.com/vuejs/vue-cli/tree/dev/packages/@vue/cli-plugin-vuex#readme", "publishConfig": {"access": "public"}, "devDependencies": {"@vue/cli-test-utils": "^4.5.19"}, "peerDependencies": {"@vue/cli-service": "^3.0.0 || ^4.0.0-0"}, "gitHead": "bef7a67566585876d56fa0e41b364675467bba8f"}