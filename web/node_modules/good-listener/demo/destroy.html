<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Destroy</title>
</head>
<body>
    <!-- 1. Write some markup -->
    <button class="target">Click me</button>
    <button class="target">Click me</button>
    <button class="target">Click me</button>

    <!-- 2. Include library -->
    <script src="../dist/good-listener.js"></script>

    <!-- 3. Remove listener by calling the destroy function -->
    <script>
    var listener = listen('.target', 'click', function(e) {
        console.info(e);
    });

    listener.destroy();
    </script>
</body>
</html>
