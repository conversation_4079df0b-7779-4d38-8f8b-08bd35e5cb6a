module.exports.generateExport = require('./generate-export');
module.exports.generateImport = require('./generate-import');
module.exports.generateSpritePlaceholder = require('./generate-sprite-placeholder');
module.exports.getAllModules = require('./get-all-modules');
// module.exports.getLoaderOptions = require('./get-loader-options');
module.exports.getModuleChunk = require('./get-module-chunk');
module.exports.getWebpackVersion = require('./get-webpack-version');
module.exports.getMatchedRule = require('./get-matched-rule');
module.exports.isModuleShouldBeExtracted = require('./is-module-should-be-extracted');
module.exports.interpolate = require('./interpolate');
module.exports.isWebpack1 = require('./is-webpack-1');
module.exports.MappedList = require('./mapped-list');
module.exports.normalizeRule = require('./normalize-rule');
module.exports.replaceInModuleSource = require('./replace-in-module-source');
module.exports.replaceSpritePlaceholder = require('./replace-sprite-placeholder');
module.exports.stringify = require('./stringify');
module.exports.stringifySymbol = require('./stringify-symbol');
