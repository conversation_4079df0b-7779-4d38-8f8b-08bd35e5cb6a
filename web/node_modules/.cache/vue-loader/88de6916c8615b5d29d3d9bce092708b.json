{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\git\\huida-reinsurance\\web\\src\\views\\tool\\build\\RightPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\views\\tool\\build\\RightPanel.vue", "mtime": 1722236072000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750743148245}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750743149606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGlzQXJyYXkgfSBmcm9tICd1dGlsJwppbXBvcnQgZHJhZ2dhYmxlIGZyb20gJ3Z1ZWRyYWdnYWJsZScKaW1wb3J0IFRyZWVOb2RlRGlhbG9nIGZyb20gJy4vVHJlZU5vZGVEaWFsb2cnCmltcG9ydCB7IGlzTnVtYmVyU3RyIH0gZnJvbSAnQC91dGlscy9pbmRleCcKaW1wb3J0IEljb25zRGlhbG9nIGZyb20gJy4vSWNvbnNEaWFsb2cnCmltcG9ydCB7CiAgaW5wdXRDb21wb25lbnRzLAogIHNlbGVjdENvbXBvbmVudHMsCiAgbGF5b3V0Q29tcG9uZW50cwp9IGZyb20gJ0AvdXRpbHMvZ2VuZXJhdG9yL2NvbmZpZycKCmNvbnN0IGRhdGVUaW1lRm9ybWF0ID0gewogIGRhdGU6ICd5eXl5LU1NLWRkJywKICB3ZWVrOiAneXl5eSDnrKwgV1cg5ZGoJywKICBtb250aDogJ3l5eXktTU0nLAogIHllYXI6ICd5eXl5JywKICBkYXRldGltZTogJ3l5eXktTU0tZGQgSEg6bW06c3MnLAogIGRhdGVyYW5nZTogJ3l5eXktTU0tZGQnLAogIG1vbnRocmFuZ2U6ICd5eXl5LU1NJywKICBkYXRldGltZXJhbmdlOiAneXl5eS1NTS1kZCBISDptbTpzcycKfQoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIGRyYWdnYWJsZSwKICAgIFRyZWVOb2RlRGlhbG9nLAogICAgSWNvbnNEaWFsb2cKICB9LAogIHByb3BzOiBbJ3Nob3dGaWVsZCcsICdhY3RpdmVEYXRhJywgJ2Zvcm1Db25mJ10sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGN1cnJlbnRUYWI6ICdmaWVsZCcsCiAgICAgIGN1cnJlbnROb2RlOiBudWxsLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgaWNvbnNWaXNpYmxlOiBmYWxzZSwKICAgICAgY3VycmVudEljb25Nb2RlbDogbnVsbCwKICAgICAgZGF0ZVR5cGVPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfml6UoZGF0ZSknLAogICAgICAgICAgdmFsdWU6ICdkYXRlJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflkagod2VlayknLAogICAgICAgICAgdmFsdWU6ICd3ZWVrJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfmnIgobW9udGgpJywKICAgICAgICAgIHZhbHVlOiAnbW9udGgnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+W5tCh5ZWFyKScsCiAgICAgICAgICB2YWx1ZTogJ3llYXInCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+aXpeacn+aXtumXtChkYXRldGltZSknLAogICAgICAgICAgdmFsdWU6ICdkYXRldGltZScKICAgICAgICB9CiAgICAgIF0sCiAgICAgIGRhdGVSYW5nZVR5cGVPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfml6XmnJ/ojIPlm7QoZGF0ZXJhbmdlKScsCiAgICAgICAgICB2YWx1ZTogJ2RhdGVyYW5nZScKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pyI6IyD5Zu0KG1vbnRocmFuZ2UpJywKICAgICAgICAgIHZhbHVlOiAnbW9udGhyYW5nZScKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pel5pyf5pe26Ze06IyD5Zu0KGRhdGV0aW1lcmFuZ2UpJywKICAgICAgICAgIHZhbHVlOiAnZGF0ZXRpbWVyYW5nZScKICAgICAgICB9CiAgICAgIF0sCiAgICAgIGNvbG9yRm9ybWF0T3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnaGV4JywKICAgICAgICAgIHZhbHVlOiAnaGV4JwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdyZ2InLAogICAgICAgICAgdmFsdWU6ICdyZ2InCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ3JnYmEnLAogICAgICAgICAgdmFsdWU6ICdyZ2JhJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdoc3YnLAogICAgICAgICAgdmFsdWU6ICdoc3YnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ2hzbCcsCiAgICAgICAgICB2YWx1ZTogJ2hzbCcKICAgICAgICB9CiAgICAgIF0sCiAgICAgIGp1c3RpZnlPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdzdGFydCcsCiAgICAgICAgICB2YWx1ZTogJ3N0YXJ0JwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdlbmQnLAogICAgICAgICAgdmFsdWU6ICdlbmQnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ2NlbnRlcicsCiAgICAgICAgICB2YWx1ZTogJ2NlbnRlcicKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnc3BhY2UtYXJvdW5kJywKICAgICAgICAgIHZhbHVlOiAnc3BhY2UtYXJvdW5kJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICdzcGFjZS1iZXR3ZWVuJywKICAgICAgICAgIHZhbHVlOiAnc3BhY2UtYmV0d2VlbicKICAgICAgICB9CiAgICAgIF0sCiAgICAgIGxheW91dFRyZWVQcm9wczogewogICAgICAgIGxhYmVsKGRhdGEsIG5vZGUpIHsKICAgICAgICAgIHJldHVybiBkYXRhLmNvbXBvbmVudE5hbWUgfHwgYCR7ZGF0YS5sYWJlbH06ICR7ZGF0YS52TW9kZWx9YAogICAgICAgIH0KICAgICAgfQogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGRvY3VtZW50TGluaygpIHsKICAgICAgcmV0dXJuICgKICAgICAgICB0aGlzLmFjdGl2ZURhdGEuZG9jdW1lbnQKICAgICAgICB8fCAnaHR0cHM6Ly9lbGVtZW50LmVsZW1lLmNuLyMvemgtQ04vY29tcG9uZW50L2luc3RhbGxhdGlvbicKICAgICAgKQogICAgfSwKICAgIGRhdGVPcHRpb25zKCkgewogICAgICBpZiAoCiAgICAgICAgdGhpcy5hY3RpdmVEYXRhLnR5cGUgIT09IHVuZGVmaW5lZAogICAgICAgICYmIHRoaXMuYWN0aXZlRGF0YS50YWcgPT09ICdlbC1kYXRlLXBpY2tlcicKICAgICAgKSB7CiAgICAgICAgaWYgKHRoaXMuYWN0aXZlRGF0YVsnc3RhcnQtcGxhY2Vob2xkZXInXSA9PT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICByZXR1cm4gdGhpcy5kYXRlVHlwZU9wdGlvbnMKICAgICAgICB9CiAgICAgICAgcmV0dXJuIHRoaXMuZGF0ZVJhbmdlVHlwZU9wdGlvbnMKICAgICAgfQogICAgICByZXR1cm4gW10KICAgIH0sCiAgICB0YWdMaXN0KCkgewogICAgICByZXR1cm4gWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn6L6T5YWl5Z6L57uE5Lu2JywKICAgICAgICAgIG9wdGlvbnM6IGlucHV0Q29tcG9uZW50cwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfpgInmi6nlnovnu4Tku7YnLAogICAgICAgICAgb3B0aW9uczogc2VsZWN0Q29tcG9uZW50cwogICAgICAgIH0KICAgICAgXQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgYWRkUmVnKCkgewogICAgICB0aGlzLmFjdGl2ZURhdGEucmVnTGlzdC5wdXNoKHsKICAgICAgICBwYXR0ZXJuOiAnJywKICAgICAgICBtZXNzYWdlOiAnJwogICAgICB9KQogICAgfSwKICAgIGFkZFNlbGVjdEl0ZW0oKSB7CiAgICAgIHRoaXMuYWN0aXZlRGF0YS5vcHRpb25zLnB1c2goewogICAgICAgIGxhYmVsOiAnJywKICAgICAgICB2YWx1ZTogJycKICAgICAgfSkKICAgIH0sCiAgICBhZGRUcmVlSXRlbSgpIHsKICAgICAgKyt0aGlzLmlkR2xvYmFsCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy5jdXJyZW50Tm9kZSA9IHRoaXMuYWN0aXZlRGF0YS5vcHRpb25zCiAgICB9LAogICAgcmVuZGVyQ29udGVudChoLCB7IG5vZGUsIGRhdGEsIHN0b3JlIH0pIHsKICAgICAgcmV0dXJuICgKICAgICAgICA8ZGl2IGNsYXNzPSJjdXN0b20tdHJlZS1ub2RlIj4KICAgICAgICAgIDxzcGFuPntub2RlLmxhYmVsfTwvc3Bhbj4KICAgICAgICAgIDxzcGFuIGNsYXNzPSJub2RlLW9wZXJhdGlvbiI+CiAgICAgICAgICAgIDxpIG9uLWNsaWNrPXsoKSA9PiB0aGlzLmFwcGVuZChkYXRhKX0KICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1wbHVzIgogICAgICAgICAgICAgIHRpdGxlPSLmt7vliqAiCiAgICAgICAgICAgID48L2k+CiAgICAgICAgICAgIDxpIG9uLWNsaWNrPXsoKSA9PiB0aGlzLnJlbW92ZShub2RlLCBkYXRhKX0KICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1kZWxldGUiCiAgICAgICAgICAgICAgdGl0bGU9IuWIoOmZpCIKICAgICAgICAgICAgPjwvaT4KICAgICAgICAgIDwvc3Bhbj4KICAgICAgICA8L2Rpdj4KICAgICAgKQogICAgfSwKICAgIGFwcGVuZChkYXRhKSB7CiAgICAgIGlmICghZGF0YS5jaGlsZHJlbikgewogICAgICAgIHRoaXMuJHNldChkYXRhLCAnY2hpbGRyZW4nLCBbXSkKICAgICAgfQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlCiAgICAgIHRoaXMuY3VycmVudE5vZGUgPSBkYXRhLmNoaWxkcmVuCiAgICB9LAogICAgcmVtb3ZlKG5vZGUsIGRhdGEpIHsKICAgICAgY29uc3QgeyBwYXJlbnQgfSA9IG5vZGUKICAgICAgY29uc3QgY2hpbGRyZW4gPSBwYXJlbnQuZGF0YS5jaGlsZHJlbiB8fCBwYXJlbnQuZGF0YQogICAgICBjb25zdCBpbmRleCA9IGNoaWxkcmVuLmZpbmRJbmRleChkID0+IGQuaWQgPT09IGRhdGEuaWQpCiAgICAgIGNoaWxkcmVuLnNwbGljZShpbmRleCwgMSkKICAgIH0sCiAgICBhZGROb2RlKGRhdGEpIHsKICAgICAgdGhpcy5jdXJyZW50Tm9kZS5wdXNoKGRhdGEpCiAgICB9LAogICAgc2V0T3B0aW9uVmFsdWUoaXRlbSwgdmFsKSB7CiAgICAgIGl0ZW0udmFsdWUgPSBpc051bWJlclN0cih2YWwpID8gK3ZhbCA6IHZhbAogICAgfSwKICAgIHNldERlZmF1bHRWYWx1ZSh2YWwpIHsKICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsKSkgewogICAgICAgIHJldHVybiB2YWwuam9pbignLCcpCiAgICAgIH0KICAgICAgaWYgKFsnc3RyaW5nJywgJ251bWJlciddLmluZGV4T2YodmFsKSA+IC0xKSB7CiAgICAgICAgcmV0dXJuIHZhbAogICAgICB9CiAgICAgIGlmICh0eXBlb2YgdmFsID09PSAnYm9vbGVhbicpIHsKICAgICAgICByZXR1cm4gYCR7dmFsfWAKICAgICAgfQogICAgICByZXR1cm4gdmFsCiAgICB9LAogICAgb25EZWZhdWx0VmFsdWVJbnB1dChzdHIpIHsKICAgICAgaWYgKGlzQXJyYXkodGhpcy5hY3RpdmVEYXRhLmRlZmF1bHRWYWx1ZSkpIHsKICAgICAgICAvLyDmlbDnu4QKICAgICAgICB0aGlzLiRzZXQoCiAgICAgICAgICB0aGlzLmFjdGl2ZURhdGEsCiAgICAgICAgICAnZGVmYXVsdFZhbHVlJywKICAgICAgICAgIHN0ci5zcGxpdCgnLCcpLm1hcCh2YWwgPT4gKGlzTnVtYmVyU3RyKHZhbCkgPyArdmFsIDogdmFsKSkKICAgICAgICApCiAgICAgIH0gZWxzZSBpZiAoWyd0cnVlJywgJ2ZhbHNlJ10uaW5kZXhPZihzdHIpID4gLTEpIHsKICAgICAgICAvLyDluIPlsJQKICAgICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCAnZGVmYXVsdFZhbHVlJywgSlNPTi5wYXJzZShzdHIpKQogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWtl+espuS4suWSjOaVsOWtlwogICAgICAgIHRoaXMuJHNldCgKICAgICAgICAgIHRoaXMuYWN0aXZlRGF0YSwKICAgICAgICAgICdkZWZhdWx0VmFsdWUnLAogICAgICAgICAgaXNOdW1iZXJTdHIoc3RyKSA/ICtzdHIgOiBzdHIKICAgICAgICApCiAgICAgIH0KICAgIH0sCiAgICBvblN3aXRjaFZhbHVlSW5wdXQodmFsLCBuYW1lKSB7CiAgICAgIGlmIChbJ3RydWUnLCAnZmFsc2UnXS5pbmRleE9mKHZhbCkgPiAtMSkgewogICAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEsIG5hbWUsIEpTT04ucGFyc2UodmFsKSkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCBuYW1lLCBpc051bWJlclN0cih2YWwpID8gK3ZhbCA6IHZhbCkKICAgICAgfQogICAgfSwKICAgIHNldFRpbWVWYWx1ZSh2YWwsIHR5cGUpIHsKICAgICAgY29uc3QgdmFsdWVGb3JtYXQgPSB0eXBlID09PSAnd2VlaycgPyBkYXRlVGltZUZvcm1hdC5kYXRlIDogdmFsCiAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEsICdkZWZhdWx0VmFsdWUnLCBudWxsKQogICAgICB0aGlzLiRzZXQodGhpcy5hY3RpdmVEYXRhLCAndmFsdWUtZm9ybWF0JywgdmFsdWVGb3JtYXQpCiAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEsICdmb3JtYXQnLCB2YWwpCiAgICB9LAogICAgc3BhbkNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5mb3JtQ29uZi5zcGFuID0gdmFsCiAgICB9LAogICAgbXVsdGlwbGVDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmFjdGl2ZURhdGEsICdkZWZhdWx0VmFsdWUnLCB2YWwgPyBbXSA6ICcnKQogICAgfSwKICAgIGRhdGVUeXBlQ2hhbmdlKHZhbCkgewogICAgICB0aGlzLnNldFRpbWVWYWx1ZShkYXRlVGltZUZvcm1hdFt2YWxdLCB2YWwpCiAgICB9LAogICAgcmFuZ2VDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJHNldCgKICAgICAgICB0aGlzLmFjdGl2ZURhdGEsCiAgICAgICAgJ2RlZmF1bHRWYWx1ZScsCiAgICAgICAgdmFsID8gW3RoaXMuYWN0aXZlRGF0YS5taW4sIHRoaXMuYWN0aXZlRGF0YS5tYXhdIDogdGhpcy5hY3RpdmVEYXRhLm1pbgogICAgICApCiAgICB9LAogICAgcmF0ZVRleHRDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwpIHRoaXMuYWN0aXZlRGF0YVsnc2hvdy1zY29yZSddID0gZmFsc2UKICAgIH0sCiAgICByYXRlU2NvcmVDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwpIHRoaXMuYWN0aXZlRGF0YVsnc2hvdy10ZXh0J10gPSBmYWxzZQogICAgfSwKICAgIGNvbG9yRm9ybWF0Q2hhbmdlKHZhbCkgewogICAgICB0aGlzLmFjdGl2ZURhdGEuZGVmYXVsdFZhbHVlID0gbnVsbAogICAgICB0aGlzLmFjdGl2ZURhdGFbJ3Nob3ctYWxwaGEnXSA9IHZhbC5pbmRleE9mKCdhJykgPiAtMQogICAgICB0aGlzLmFjdGl2ZURhdGEucmVuZGVyS2V5ID0gK25ldyBEYXRlKCkgLy8g5pu05pawcmVuZGVyS2V5LOmHjeaWsOa4suafk+ivpee7hOS7tgogICAgfSwKICAgIG9wZW5JY29uc0RpYWxvZyhtb2RlbCkgewogICAgICB0aGlzLmljb25zVmlzaWJsZSA9IHRydWUKICAgICAgdGhpcy5jdXJyZW50SWNvbk1vZGVsID0gbW9kZWwKICAgIH0sCiAgICBzZXRJY29uKHZhbCkgewogICAgICB0aGlzLmFjdGl2ZURhdGFbdGhpcy5jdXJyZW50SWNvbk1vZGVsXSA9IHZhbAogICAgfSwKICAgIHRhZ0NoYW5nZSh0YWdJY29uKSB7CiAgICAgIGxldCB0YXJnZXQgPSBpbnB1dENvbXBvbmVudHMuZmluZChpdGVtID0+IGl0ZW0udGFnSWNvbiA9PT0gdGFnSWNvbikKICAgICAgaWYgKCF0YXJnZXQpIHRhcmdldCA9IHNlbGVjdENvbXBvbmVudHMuZmluZChpdGVtID0+IGl0ZW0udGFnSWNvbiA9PT0gdGFnSWNvbikKICAgICAgdGhpcy4kZW1pdCgndGFnLWNoYW5nZScsIHRhcmdldCkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["RightPanel.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8jBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RightPanel.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<template>\n  <div class=\"right-board\">\n    <el-tabs v-model=\"currentTab\" class=\"center-tabs\">\n      <el-tab-pane label=\"组件属性\" name=\"field\" />\n      <el-tab-pane label=\"表单属性\" name=\"form\" />\n    </el-tabs>\n    <div class=\"field-box\">\n      <a class=\"document-link\" target=\"_blank\" :href=\"documentLink\" title=\"查看组件文档\">\n        <i class=\"el-icon-link\" />\n      </a>\n      <el-scrollbar class=\"right-scrollbar\">\n        <!-- 组件属性 -->\n        <el-form v-show=\"currentTab==='field' && showField\" size=\"small\" label-width=\"90px\">\n          <el-form-item v-if=\"activeData.changeTag\" label=\"组件类型\">\n            <el-select\n              v-model=\"activeData.tagIcon\"\n              placeholder=\"请选择组件类型\"\n              :style=\"{width: '100%'}\"\n              @change=\"tagChange\"\n            >\n              <el-option-group v-for=\"group in tagList\" :key=\"group.label\" :label=\"group.label\">\n                <el-option\n                  v-for=\"item in group.options\"\n                  :key=\"item.label\"\n                  :label=\"item.label\"\n                  :value=\"item.tagIcon\"\n                >\n                  <svg-icon class=\"node-icon\" :icon-class=\"item.tagIcon\" />\n                  <span> {{ item.label }}</span>\n                </el-option>\n              </el-option-group>\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.vModel!==undefined\" label=\"字段名\">\n            <el-input v-model=\"activeData.vModel\" placeholder=\"请输入字段名（v-model）\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.componentName!==undefined\" label=\"组件名\">\n            {{ activeData.componentName }}\n          </el-form-item>\n          <el-form-item v-if=\"activeData.label!==undefined\" label=\"标题\">\n            <el-input v-model=\"activeData.label\" placeholder=\"请输入标题\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.placeholder!==undefined\" label=\"占位提示\">\n            <el-input v-model=\"activeData.placeholder\" placeholder=\"请输入占位提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['start-placeholder']!==undefined\" label=\"开始占位\">\n            <el-input v-model=\"activeData['start-placeholder']\" placeholder=\"请输入占位提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['end-placeholder']!==undefined\" label=\"结束占位\">\n            <el-input v-model=\"activeData['end-placeholder']\" placeholder=\"请输入占位提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.span!==undefined\" label=\"表单栅格\">\n            <el-slider v-model=\"activeData.span\" :max=\"24\" :min=\"1\" :marks=\"{12:''}\" @change=\"spanChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.layout==='rowFormItem'\" label=\"栅格间隔\">\n            <el-input-number v-model=\"activeData.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.layout==='rowFormItem'\" label=\"布局模式\">\n            <el-radio-group v-model=\"activeData.type\">\n              <el-radio-button label=\"default\" />\n              <el-radio-button label=\"flex\" />\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.justify!==undefined&&activeData.type==='flex'\" label=\"水平排列\">\n            <el-select v-model=\"activeData.justify\" placeholder=\"请选择水平排列\" :style=\"{width: '100%'}\">\n              <el-option\n                v-for=\"(item, index) in justifyOptions\"\n                :key=\"index\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.align!==undefined&&activeData.type==='flex'\" label=\"垂直排列\">\n            <el-radio-group v-model=\"activeData.align\">\n              <el-radio-button label=\"top\" />\n              <el-radio-button label=\"middle\" />\n              <el-radio-button label=\"bottom\" />\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.labelWidth!==undefined\" label=\"标签宽度\">\n            <el-input v-model.number=\"activeData.labelWidth\" type=\"number\" placeholder=\"请输入标签宽度\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.style&&activeData.style.width!==undefined\" label=\"组件宽度\">\n            <el-input v-model=\"activeData.style.width\" placeholder=\"请输入组件宽度\" clearable />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.vModel!==undefined\" label=\"默认值\">\n            <el-input\n              :value=\"setDefaultValue(activeData.defaultValue)\"\n              placeholder=\"请输入默认值\"\n              @input=\"onDefaultValueInput\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag==='el-checkbox-group'\" label=\"至少应选\">\n            <el-input-number\n              :value=\"activeData.min\"\n              :min=\"0\"\n              placeholder=\"至少应选\"\n              @input=\"$set(activeData, 'min', $event?$event:undefined)\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag==='el-checkbox-group'\" label=\"最多可选\">\n            <el-input-number\n              :value=\"activeData.max\"\n              :min=\"0\"\n              placeholder=\"最多可选\"\n              @input=\"$set(activeData, 'max', $event?$event:undefined)\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.prepend!==undefined\" label=\"前缀\">\n            <el-input v-model=\"activeData.prepend\" placeholder=\"请输入前缀\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.append!==undefined\" label=\"后缀\">\n            <el-input v-model=\"activeData.append\" placeholder=\"请输入后缀\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['prefix-icon']!==undefined\" label=\"前图标\">\n            <el-input v-model=\"activeData['prefix-icon']\" placeholder=\"请输入前图标名称\">\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('prefix-icon')\">\n                选择\n              </el-button>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['suffix-icon'] !== undefined\" label=\"后图标\">\n            <el-input v-model=\"activeData['suffix-icon']\" placeholder=\"请输入后图标名称\">\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('suffix-icon')\">\n                选择\n              </el-button>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"选项分隔符\">\n            <el-input v-model=\"activeData.separator\" placeholder=\"请输入选项分隔符\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最小行数\">\n            <el-input-number v-model=\"activeData.autosize.minRows\" :min=\"1\" placeholder=\"最小行数\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最大行数\">\n            <el-input-number v-model=\"activeData.autosize.maxRows\" :min=\"1\" placeholder=\"最大行数\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.min !== undefined\" label=\"最小值\">\n            <el-input-number v-model=\"activeData.min\" placeholder=\"最小值\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.max !== undefined\" label=\"最大值\">\n            <el-input-number v-model=\"activeData.max\" placeholder=\"最大值\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.step !== undefined\" label=\"步长\">\n            <el-input-number v-model=\"activeData.step\" placeholder=\"步数\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"精度\">\n            <el-input-number v-model=\"activeData.precision\" :min=\"0\" placeholder=\"精度\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"按钮位置\">\n            <el-radio-group v-model=\"activeData['controls-position']\">\n              <el-radio-button label=\"\">\n                默认\n              </el-radio-button>\n              <el-radio-button label=\"right\">\n                右侧\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.maxlength !== undefined\" label=\"最多输入\">\n            <el-input v-model=\"activeData.maxlength\" placeholder=\"请输入字符长度\">\n              <template slot=\"append\">\n                个字符\n              </template>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['active-text'] !== undefined\" label=\"开启提示\">\n            <el-input v-model=\"activeData['active-text']\" placeholder=\"请输入开启提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['inactive-text'] !== undefined\" label=\"关闭提示\">\n            <el-input v-model=\"activeData['inactive-text']\" placeholder=\"请输入关闭提示\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['active-value'] !== undefined\" label=\"开启值\">\n            <el-input\n              :value=\"setDefaultValue(activeData['active-value'])\"\n              placeholder=\"请输入开启值\"\n              @input=\"onSwitchValueInput($event, 'active-value')\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['inactive-value'] !== undefined\" label=\"关闭值\">\n            <el-input\n              :value=\"setDefaultValue(activeData['inactive-value'])\"\n              placeholder=\"请输入关闭值\"\n              @input=\"onSwitchValueInput($event, 'inactive-value')\"\n            />\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.type !== undefined && 'el-date-picker' === activeData.tag\"\n            label=\"时间类型\"\n          >\n            <el-select\n              v-model=\"activeData.type\"\n              placeholder=\"请选择时间类型\"\n              :style=\"{ width: '100%' }\"\n              @change=\"dateTypeChange\"\n            >\n              <el-option\n                v-for=\"(item, index) in dateOptions\"\n                :key=\"index\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.name !== undefined\" label=\"文件字段名\">\n            <el-input v-model=\"activeData.name\" placeholder=\"请输入上传文件字段名\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.accept !== undefined\" label=\"文件类型\">\n            <el-select\n              v-model=\"activeData.accept\"\n              placeholder=\"请选择文件类型\"\n              :style=\"{ width: '100%' }\"\n              clearable\n            >\n              <el-option label=\"图片\" value=\"image/*\" />\n              <el-option label=\"视频\" value=\"video/*\" />\n              <el-option label=\"音频\" value=\"audio/*\" />\n              <el-option label=\"excel\" value=\".xls,.xlsx\" />\n              <el-option label=\"word\" value=\".doc,.docx\" />\n              <el-option label=\"pdf\" value=\".pdf\" />\n              <el-option label=\"txt\" value=\".txt\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.fileSize !== undefined\" label=\"文件大小\">\n            <el-input v-model.number=\"activeData.fileSize\" placeholder=\"请输入文件大小\">\n              <el-select slot=\"append\" v-model=\"activeData.sizeUnit\" :style=\"{ width: '66px' }\">\n                <el-option label=\"KB\" value=\"KB\" />\n                <el-option label=\"MB\" value=\"MB\" />\n                <el-option label=\"GB\" value=\"GB\" />\n              </el-select>\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"activeData.action !== undefined\" label=\"上传地址\">\n            <el-input v-model=\"activeData.action\" placeholder=\"请输入上传地址\" clearable />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['list-type'] !== undefined\" label=\"列表类型\">\n            <el-radio-group v-model=\"activeData['list-type']\" size=\"small\">\n              <el-radio-button label=\"text\">\n                text\n              </el-radio-button>\n              <el-radio-button label=\"picture\">\n                picture\n              </el-radio-button>\n              <el-radio-button label=\"picture-card\">\n                picture-card\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.buttonText !== undefined\"\n            v-show=\"'picture-card' !== activeData['list-type']\"\n            label=\"按钮文字\"\n          >\n            <el-input v-model=\"activeData.buttonText\" placeholder=\"请输入按钮文字\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['range-separator'] !== undefined\" label=\"分隔符\">\n            <el-input v-model=\"activeData['range-separator']\" placeholder=\"请输入分隔符\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['picker-options'] !== undefined\" label=\"时间段\">\n            <el-input\n              v-model=\"activeData['picker-options'].selectableRange\"\n              placeholder=\"请输入时间段\"\n            />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.format !== undefined\" label=\"时间格式\">\n            <el-input\n              :value=\"activeData.format\"\n              placeholder=\"请输入时间格式\"\n              @input=\"setTimeValue($event)\"\n            />\n          </el-form-item>\n          <template v-if=\"['el-checkbox-group', 'el-radio-group', 'el-select'].indexOf(activeData.tag) > -1\">\n            <el-divider>选项</el-divider>\n            <draggable\n              :list=\"activeData.options\"\n              :animation=\"340\"\n              group=\"selectItem\"\n              handle=\".option-drag\"\n            >\n              <div v-for=\"(item, index) in activeData.options\" :key=\"index\" class=\"select-item\">\n                <div class=\"select-line-icon option-drag\">\n                  <i class=\"el-icon-s-operation\" />\n                </div>\n                <el-input v-model=\"item.label\" placeholder=\"选项名\" size=\"small\" />\n                <el-input\n                  placeholder=\"选项值\"\n                  size=\"small\"\n                  :value=\"item.value\"\n                  @input=\"setOptionValue(item, $event)\"\n                />\n                <div class=\"close-btn select-line-icon\" @click=\"activeData.options.splice(index, 1)\">\n                  <i class=\"el-icon-remove-outline\" />\n                </div>\n              </div>\n            </draggable>\n            <div style=\"margin-left: 20px;\">\n              <el-button\n                style=\"padding-bottom: 0\"\n                icon=\"el-icon-circle-plus-outline\"\n                type=\"text\"\n                @click=\"addSelectItem\"\n              >\n                添加选项\n              </el-button>\n            </div>\n            <el-divider />\n          </template>\n\n          <template v-if=\"['el-cascader'].indexOf(activeData.tag) > -1\">\n            <el-divider>选项</el-divider>\n            <el-form-item label=\"数据类型\">\n              <el-radio-group v-model=\"activeData.dataType\" size=\"small\">\n                <el-radio-button label=\"dynamic\">\n                  动态数据\n                </el-radio-button>\n                <el-radio-button label=\"static\">\n                  静态数据\n                </el-radio-button>\n              </el-radio-group>\n            </el-form-item>\n\n            <template v-if=\"activeData.dataType === 'dynamic'\">\n              <el-form-item label=\"标签键名\">\n                <el-input v-model=\"activeData.labelKey\" placeholder=\"请输入标签键名\" />\n              </el-form-item>\n              <el-form-item label=\"值键名\">\n                <el-input v-model=\"activeData.valueKey\" placeholder=\"请输入值键名\" />\n              </el-form-item>\n              <el-form-item label=\"子级键名\">\n                <el-input v-model=\"activeData.childrenKey\" placeholder=\"请输入子级键名\" />\n              </el-form-item>\n            </template>\n\n            <el-tree\n              v-if=\"activeData.dataType === 'static'\"\n              draggable\n              :data=\"activeData.options\"\n              node-key=\"id\"\n              :expand-on-click-node=\"false\"\n              :render-content=\"renderContent\"\n            />\n            <div v-if=\"activeData.dataType === 'static'\" style=\"margin-left: 20px\">\n              <el-button\n                style=\"padding-bottom: 0\"\n                icon=\"el-icon-circle-plus-outline\"\n                type=\"text\"\n                @click=\"addTreeItem\"\n              >\n                添加父级\n              </el-button>\n            </div>\n            <el-divider />\n          </template>\n\n          <el-form-item v-if=\"activeData.optionType !== undefined\" label=\"选项样式\">\n            <el-radio-group v-model=\"activeData.optionType\">\n              <el-radio-button label=\"default\">\n                默认\n              </el-radio-button>\n              <el-radio-button label=\"button\">\n                按钮\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['active-color'] !== undefined\" label=\"开启颜色\">\n            <el-color-picker v-model=\"activeData['active-color']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['inactive-color'] !== undefined\" label=\"关闭颜色\">\n            <el-color-picker v-model=\"activeData['inactive-color']\" />\n          </el-form-item>\n\n          <el-form-item v-if=\"activeData['allow-half'] !== undefined\" label=\"允许半选\">\n            <el-switch v-model=\"activeData['allow-half']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-text'] !== undefined\" label=\"辅助文字\">\n            <el-switch v-model=\"activeData['show-text']\" @change=\"rateTextChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-score'] !== undefined\" label=\"显示分数\">\n            <el-switch v-model=\"activeData['show-score']\" @change=\"rateScoreChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-stops'] !== undefined\" label=\"显示间断点\">\n            <el-switch v-model=\"activeData['show-stops']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.range !== undefined\" label=\"范围选择\">\n            <el-switch v-model=\"activeData.range\" @change=\"rangeChange\" />\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.border !== undefined && activeData.optionType === 'default'\"\n            label=\"是否带边框\"\n          >\n            <el-switch v-model=\"activeData.border\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-color-picker'\" label=\"颜色格式\">\n            <el-select\n              v-model=\"activeData['color-format']\"\n              placeholder=\"请选择颜色格式\"\n              :style=\"{ width: '100%' }\"\n              @change=\"colorFormatChange\"\n            >\n              <el-option\n                v-for=\"(item, index) in colorFormatOptions\"\n                :key=\"index\"\n                :label=\"item.label\"\n                :value=\"item.value\"\n              />\n            </el-select>\n          </el-form-item>\n          <el-form-item\n            v-if=\"activeData.size !== undefined &&\n              (activeData.optionType === 'button' ||\n                activeData.border ||\n                activeData.tag === 'el-color-picker')\"\n            label=\"选项尺寸\"\n          >\n            <el-radio-group v-model=\"activeData.size\">\n              <el-radio-button label=\"medium\">\n                中等\n              </el-radio-button>\n              <el-radio-button label=\"small\">\n                较小\n              </el-radio-button>\n              <el-radio-button label=\"mini\">\n                迷你\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"activeData['show-word-limit'] !== undefined\" label=\"输入统计\">\n            <el-switch v-model=\"activeData['show-word-limit']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-input-number'\" label=\"严格步数\">\n            <el-switch v-model=\"activeData['step-strictly']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"是否多选\">\n            <el-switch v-model=\"activeData.props.props.multiple\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"展示全路径\">\n            <el-switch v-model=\"activeData['show-all-levels']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-cascader'\" label=\"可否筛选\">\n            <el-switch v-model=\"activeData.filterable\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.clearable !== undefined\" label=\"能否清空\">\n            <el-switch v-model=\"activeData.clearable\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.showTip !== undefined\" label=\"显示提示\">\n            <el-switch v-model=\"activeData.showTip\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.multiple !== undefined\" label=\"多选文件\">\n            <el-switch v-model=\"activeData.multiple\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData['auto-upload'] !== undefined\" label=\"自动上传\">\n            <el-switch v-model=\"activeData['auto-upload']\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.readonly !== undefined\" label=\"是否只读\">\n            <el-switch v-model=\"activeData.readonly\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.disabled !== undefined\" label=\"是否禁用\">\n            <el-switch v-model=\"activeData.disabled\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-select'\" label=\"是否可搜索\">\n            <el-switch v-model=\"activeData.filterable\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.tag === 'el-select'\" label=\"是否多选\">\n            <el-switch v-model=\"activeData.multiple\" @change=\"multipleChange\" />\n          </el-form-item>\n          <el-form-item v-if=\"activeData.required !== undefined\" label=\"是否必填\">\n            <el-switch v-model=\"activeData.required\" />\n          </el-form-item>\n\n          <template v-if=\"activeData.layoutTree\">\n            <el-divider>布局结构树</el-divider>\n            <el-tree\n              :data=\"[activeData]\"\n              :props=\"layoutTreeProps\"\n              node-key=\"renderKey\"\n              default-expand-all\n              draggable\n            >\n              <span slot-scope=\"{ node, data }\">\n                <span class=\"node-label\">\n                  <svg-icon class=\"node-icon\" :icon-class=\"data.tagIcon\" />\n                  {{ node.label }}\n                </span>\n              </span>\n            </el-tree>\n          </template>\n\n          <template v-if=\"activeData.layout === 'colFormItem' && activeData.tag !== 'el-button'\">\n            <el-divider>正则校验</el-divider>\n            <div\n              v-for=\"(item, index) in activeData.regList\"\n              :key=\"index\"\n              class=\"reg-item\"\n            >\n              <span class=\"close-btn\" @click=\"activeData.regList.splice(index, 1)\">\n                <i class=\"el-icon-close\" />\n              </span>\n              <el-form-item label=\"表达式\">\n                <el-input v-model=\"item.pattern\" placeholder=\"请输入正则\" />\n              </el-form-item>\n              <el-form-item label=\"错误提示\" style=\"margin-bottom:0\">\n                <el-input v-model=\"item.message\" placeholder=\"请输入错误提示\" />\n              </el-form-item>\n            </div>\n            <div style=\"margin-left: 20px\">\n              <el-button icon=\"el-icon-circle-plus-outline\" type=\"text\" @click=\"addReg\">\n                添加规则\n              </el-button>\n            </div>\n          </template>\n        </el-form>\n        <!-- 表单属性 -->\n        <el-form v-show=\"currentTab === 'form'\" size=\"small\" label-width=\"90px\">\n          <el-form-item label=\"表单名\">\n            <el-input v-model=\"formConf.formRef\" placeholder=\"请输入表单名（ref）\" />\n          </el-form-item>\n          <el-form-item label=\"表单模型\">\n            <el-input v-model=\"formConf.formModel\" placeholder=\"请输入数据模型\" />\n          </el-form-item>\n          <el-form-item label=\"校验模型\">\n            <el-input v-model=\"formConf.formRules\" placeholder=\"请输入校验模型\" />\n          </el-form-item>\n          <el-form-item label=\"表单尺寸\">\n            <el-radio-group v-model=\"formConf.size\">\n              <el-radio-button label=\"medium\">\n                中等\n              </el-radio-button>\n              <el-radio-button label=\"small\">\n                较小\n              </el-radio-button>\n              <el-radio-button label=\"mini\">\n                迷你\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"标签对齐\">\n            <el-radio-group v-model=\"formConf.labelPosition\">\n              <el-radio-button label=\"left\">\n                左对齐\n              </el-radio-button>\n              <el-radio-button label=\"right\">\n                右对齐\n              </el-radio-button>\n              <el-radio-button label=\"top\">\n                顶部对齐\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"标签宽度\">\n            <el-input-number v-model=\"formConf.labelWidth\" placeholder=\"标签宽度\" />\n          </el-form-item>\n          <el-form-item label=\"栅格间隔\">\n            <el-input-number v-model=\"formConf.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\n          </el-form-item>\n          <el-form-item label=\"禁用表单\">\n            <el-switch v-model=\"formConf.disabled\" />\n          </el-form-item>\n          <el-form-item label=\"表单按钮\">\n            <el-switch v-model=\"formConf.formBtns\" />\n          </el-form-item>\n          <el-form-item label=\"显示未选中组件边框\">\n            <el-switch v-model=\"formConf.unFocusedComponentBorder\" />\n          </el-form-item>\n        </el-form>\n      </el-scrollbar>\n    </div>\n\n    <treeNode-dialog :visible.sync=\"dialogVisible\" title=\"添加选项\" @commit=\"addNode\" />\n    <icons-dialog :visible.sync=\"iconsVisible\" :current=\"activeData[currentIconModel]\" @select=\"setIcon\" />\n  </div>\n</template>\n\n<script>\nimport { isArray } from 'util'\nimport draggable from 'vuedraggable'\nimport TreeNodeDialog from './TreeNodeDialog'\nimport { isNumberStr } from '@/utils/index'\nimport IconsDialog from './IconsDialog'\nimport {\n  inputComponents,\n  selectComponents,\n  layoutComponents\n} from '@/utils/generator/config'\n\nconst dateTimeFormat = {\n  date: 'yyyy-MM-dd',\n  week: 'yyyy 第 WW 周',\n  month: 'yyyy-MM',\n  year: 'yyyy',\n  datetime: 'yyyy-MM-dd HH:mm:ss',\n  daterange: 'yyyy-MM-dd',\n  monthrange: 'yyyy-MM',\n  datetimerange: 'yyyy-MM-dd HH:mm:ss'\n}\n\nexport default {\n  components: {\n    draggable,\n    TreeNodeDialog,\n    IconsDialog\n  },\n  props: ['showField', 'activeData', 'formConf'],\n  data() {\n    return {\n      currentTab: 'field',\n      currentNode: null,\n      dialogVisible: false,\n      iconsVisible: false,\n      currentIconModel: null,\n      dateTypeOptions: [\n        {\n          label: '日(date)',\n          value: 'date'\n        },\n        {\n          label: '周(week)',\n          value: 'week'\n        },\n        {\n          label: '月(month)',\n          value: 'month'\n        },\n        {\n          label: '年(year)',\n          value: 'year'\n        },\n        {\n          label: '日期时间(datetime)',\n          value: 'datetime'\n        }\n      ],\n      dateRangeTypeOptions: [\n        {\n          label: '日期范围(daterange)',\n          value: 'daterange'\n        },\n        {\n          label: '月范围(monthrange)',\n          value: 'monthrange'\n        },\n        {\n          label: '日期时间范围(datetimerange)',\n          value: 'datetimerange'\n        }\n      ],\n      colorFormatOptions: [\n        {\n          label: 'hex',\n          value: 'hex'\n        },\n        {\n          label: 'rgb',\n          value: 'rgb'\n        },\n        {\n          label: 'rgba',\n          value: 'rgba'\n        },\n        {\n          label: 'hsv',\n          value: 'hsv'\n        },\n        {\n          label: 'hsl',\n          value: 'hsl'\n        }\n      ],\n      justifyOptions: [\n        {\n          label: 'start',\n          value: 'start'\n        },\n        {\n          label: 'end',\n          value: 'end'\n        },\n        {\n          label: 'center',\n          value: 'center'\n        },\n        {\n          label: 'space-around',\n          value: 'space-around'\n        },\n        {\n          label: 'space-between',\n          value: 'space-between'\n        }\n      ],\n      layoutTreeProps: {\n        label(data, node) {\n          return data.componentName || `${data.label}: ${data.vModel}`\n        }\n      }\n    }\n  },\n  computed: {\n    documentLink() {\n      return (\n        this.activeData.document\n        || 'https://element.eleme.cn/#/zh-CN/component/installation'\n      )\n    },\n    dateOptions() {\n      if (\n        this.activeData.type !== undefined\n        && this.activeData.tag === 'el-date-picker'\n      ) {\n        if (this.activeData['start-placeholder'] === undefined) {\n          return this.dateTypeOptions\n        }\n        return this.dateRangeTypeOptions\n      }\n      return []\n    },\n    tagList() {\n      return [\n        {\n          label: '输入型组件',\n          options: inputComponents\n        },\n        {\n          label: '选择型组件',\n          options: selectComponents\n        }\n      ]\n    }\n  },\n  methods: {\n    addReg() {\n      this.activeData.regList.push({\n        pattern: '',\n        message: ''\n      })\n    },\n    addSelectItem() {\n      this.activeData.options.push({\n        label: '',\n        value: ''\n      })\n    },\n    addTreeItem() {\n      ++this.idGlobal\n      this.dialogVisible = true\n      this.currentNode = this.activeData.options\n    },\n    renderContent(h, { node, data, store }) {\n      return (\n        <div class=\"custom-tree-node\">\n          <span>{node.label}</span>\n          <span class=\"node-operation\">\n            <i on-click={() => this.append(data)}\n              class=\"el-icon-plus\"\n              title=\"添加\"\n            ></i>\n            <i on-click={() => this.remove(node, data)}\n              class=\"el-icon-delete\"\n              title=\"删除\"\n            ></i>\n          </span>\n        </div>\n      )\n    },\n    append(data) {\n      if (!data.children) {\n        this.$set(data, 'children', [])\n      }\n      this.dialogVisible = true\n      this.currentNode = data.children\n    },\n    remove(node, data) {\n      const { parent } = node\n      const children = parent.data.children || parent.data\n      const index = children.findIndex(d => d.id === data.id)\n      children.splice(index, 1)\n    },\n    addNode(data) {\n      this.currentNode.push(data)\n    },\n    setOptionValue(item, val) {\n      item.value = isNumberStr(val) ? +val : val\n    },\n    setDefaultValue(val) {\n      if (Array.isArray(val)) {\n        return val.join(',')\n      }\n      if (['string', 'number'].indexOf(val) > -1) {\n        return val\n      }\n      if (typeof val === 'boolean') {\n        return `${val}`\n      }\n      return val\n    },\n    onDefaultValueInput(str) {\n      if (isArray(this.activeData.defaultValue)) {\n        // 数组\n        this.$set(\n          this.activeData,\n          'defaultValue',\n          str.split(',').map(val => (isNumberStr(val) ? +val : val))\n        )\n      } else if (['true', 'false'].indexOf(str) > -1) {\n        // 布尔\n        this.$set(this.activeData, 'defaultValue', JSON.parse(str))\n      } else {\n        // 字符串和数字\n        this.$set(\n          this.activeData,\n          'defaultValue',\n          isNumberStr(str) ? +str : str\n        )\n      }\n    },\n    onSwitchValueInput(val, name) {\n      if (['true', 'false'].indexOf(val) > -1) {\n        this.$set(this.activeData, name, JSON.parse(val))\n      } else {\n        this.$set(this.activeData, name, isNumberStr(val) ? +val : val)\n      }\n    },\n    setTimeValue(val, type) {\n      const valueFormat = type === 'week' ? dateTimeFormat.date : val\n      this.$set(this.activeData, 'defaultValue', null)\n      this.$set(this.activeData, 'value-format', valueFormat)\n      this.$set(this.activeData, 'format', val)\n    },\n    spanChange(val) {\n      this.formConf.span = val\n    },\n    multipleChange(val) {\n      this.$set(this.activeData, 'defaultValue', val ? [] : '')\n    },\n    dateTypeChange(val) {\n      this.setTimeValue(dateTimeFormat[val], val)\n    },\n    rangeChange(val) {\n      this.$set(\n        this.activeData,\n        'defaultValue',\n        val ? [this.activeData.min, this.activeData.max] : this.activeData.min\n      )\n    },\n    rateTextChange(val) {\n      if (val) this.activeData['show-score'] = false\n    },\n    rateScoreChange(val) {\n      if (val) this.activeData['show-text'] = false\n    },\n    colorFormatChange(val) {\n      this.activeData.defaultValue = null\n      this.activeData['show-alpha'] = val.indexOf('a') > -1\n      this.activeData.renderKey = +new Date() // 更新renderKey,重新渲染该组件\n    },\n    openIconsDialog(model) {\n      this.iconsVisible = true\n      this.currentIconModel = model\n    },\n    setIcon(val) {\n      this.activeData[this.currentIconModel] = val\n    },\n    tagChange(tagIcon) {\n      let target = inputComponents.find(item => item.tagIcon === tagIcon)\n      if (!target) target = selectComponents.find(item => item.tagIcon === tagIcon)\n      this.$emit('tag-change', target)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.right-board {\n  width: 350px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  padding-top: 3px;\n  .field-box {\n    position: relative;\n    height: calc(100vh - 42px);\n    box-sizing: border-box;\n    overflow: hidden;\n  }\n  .el-scrollbar {\n    height: 100%;\n  }\n}\n.select-item {\n  display: flex;\n  border: 1px dashed #fff;\n  box-sizing: border-box;\n  & .close-btn {\n    cursor: pointer;\n    color: #f56c6c;\n  }\n  & .el-input + .el-input {\n    margin-left: 4px;\n  }\n}\n.select-item + .select-item {\n  margin-top: 4px;\n}\n.select-item.sortable-chosen {\n  border: 1px dashed #409eff;\n}\n.select-line-icon {\n  line-height: 32px;\n  font-size: 22px;\n  padding: 0 4px;\n  color: #777;\n}\n.option-drag {\n  cursor: move;\n}\n.time-range {\n  .el-date-editor {\n    width: 227px;\n  }\n  ::v-deep .el-icon-time {\n    display: none;\n  }\n}\n.document-link {\n  position: absolute;\n  display: block;\n  width: 26px;\n  height: 26px;\n  top: 0;\n  left: 0;\n  cursor: pointer;\n  background: #409eff;\n  z-index: 1;\n  border-radius: 0 0 6px 0;\n  text-align: center;\n  line-height: 26px;\n  color: #fff;\n  font-size: 18px;\n}\n.node-label{\n  font-size: 14px;\n}\n.node-icon{\n  color: #bebfc3;\n}\n</style>\n"]}]}