{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\businessReport\\index.vue?vue&type=style&index=0&id=4a6d888e&scoped=true&lang=css", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\businessReport\\index.vue", "mtime": 1722240766000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750743146578}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750743151034}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750743148205}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750743149606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKOjp2LWRlZXAgLmVsLXVwbG9hZCB7CiAgd2lkdGg6IDEwMCU7CiAgcGFkZGluZy1sZWZ0OiAyMHB4OwogIHBhZGRpbmctcmlnaHQ6IDIwcHg7Cn0KOjp2LWRlZXAgLmVsLXVwbG9hZC1kcmFnZ2VyIHsKICB3aWR0aDogMTAwJTsKfQouZGlhbG9nLWZvb3RlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOwogIHBhZGRpbmc6IDIwcHggMjBweCAwIDA7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/reinsurance/businessReport", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card header=\"报表生成\">\n      <el-form :model=\"reportParams\" ref=\"reportForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"75px\">\n        <el-form-item label=\"报表类型\" prop=\"reportType\">\n          <el-select v-model=\"reportParams.reportType\" placeholder=\"请选择报表类型\" filterable clearable>\n            <el-option v-for=\"item in dict.type.report_type\"\n              :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"账单日期\" prop=\"billingDate\">\n          <el-date-picker v-model=\"reportParams.billingDate\" type=\"daterange\" range-separator=\"至\"\n            start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" value-format=\"yyyy-MM-dd\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"getList\">查询</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <el-row :gutter=\"10\" class=\"mb8\">\n        <el-col :span=\"1.5\">\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"reportStatistics\"\n            v-has-permi=\"['reinsurance:businessReport:reportStatistics']\" :loading=\"reportStatisticsLoading\">报表统计</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\"\n            v-has-permi=\"['reinsurance:businessReport:export']\" @click=\"exportReport\">导出</el-button>\n        </el-col>\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n      </el-row>\n\n      <el-table v-loading=\"reportStatisticsQueryLoading\" :data=\"tableList.data\">\n        <el-table-column align=\"center\" label=\"序号\" width=\"60\">\n          <template slot-scope=\"scope\">\n            {{ (reportParams.pageNum - 1) * reportParams.pageSize + scope.$index + 1 }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"报表类型\" align=\"center\" prop=\"reportType\" width=\"150\">\n          <template slot-scope=\"{row}\">\n            <dict-tag :options=\"dict.type.report_type\" :value=\"row.reportType\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"报表名称\" align=\"center\" prop=\"reportName\" min-width=\"260\" show-overflow-tooltip />\n        <el-table-column label=\"账单业务时间\" align=\"center\" prop=\"startDate\" width=\"220\">\n          <template slot-scope=\"{row}\">\n            {{`${row.startDate} ~ ${row.endDate}`}}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"100\">\n          <template slot-scope=\"{row}\">\n            <dict-tag :options=\"dict.type.business_report_status\" :value=\"row.status\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作人\" align=\"center\" prop=\"status\" width=\"150\">\n          <template slot-scope=\"{row}\">\n            <dict-tag :options=\"dict.type.sys_user\" :value=\"row.createBy\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\" />\n        <el-table-column label=\"修改时间\" align=\"center\" prop=\"updateTime\" width=\"160\" />\n        <el-table-column label=\"操作\" align=\"center\" width=\"180\" fixed=\"right\">\n          <template slot-scope=\"{row}\">\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-download\" v-if=\"row.status === 0\" @click=\"downloadReport(row)\">下载</el-button>\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"deleteReport(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"tableList.total > 0\"\n        :total=\"tableList.total\"\n        :page.sync=\"reportParams.pageNum\"\n        :limit.sync=\"reportParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n  </div>\n</template>\n<script>\nimport {\n  businessReportDelete,\n  businessReportList,\n  businessReportStatistics\n} from \"@/api/reinsurance/businessReport\";\nimport {downloadFileByPathApi} from \"@/api/reinsurance/file\";\nimport {downloadFileHandler} from \"@/api/tool/file\";\n\nexport default {\n  dicts: ['report_type', 'business_report_status', 'sys_user'],\n  data() {\n    return {\n      //显示搜索条件\n      showSearch: true,\n      //报表查询loading\n      reportStatisticsQueryLoading: false,\n      //报表统计参数\n      reportParams: {\n        pageNum: 1,\n        pageSize: 10,\n        reportType: null,\n        billingDate: null\n      },\n      //报表统计loading\n      reportStatisticsLoading: false,\n      //表格数据\n      tableList: {\n        data: [],\n        total: 0\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    //报表统计\n    reportStatistics() {\n      if (!this.reportParams.billingDate || this.reportParams.billingDate.length !== 2) {\n        this.$message.error('请选择账单日期')\n        return\n      }\n      if (!this.reportParams.reportType) {\n        this.$message.error('请选择报表类型')\n        return\n      }\n      this.reportStatisticsLoading = true\n      this.reportParams.startDate = this.reportParams.billingDate[0]\n      this.reportParams.endDate = this.reportParams.billingDate[1]\n      businessReportStatistics(this.reportParams).then(res => {\n        this.$message.success('操作成功，请耐心等待')\n        //统计完成后，清空查询条件\n        this.resetQuery()\n      }).finally(() => {\n        this.reportStatisticsLoading = false\n      })\n    },\n    //报表记录查询\n    getList() {\n      this.reportStatisticsQueryLoading = true\n      if (this.reportParams.billingDate && this.reportParams.billingDate.length === 2) {\n        this.reportParams.startDate = this.reportParams.billingDate[0]\n        this.reportParams.endDate = this.reportParams.billingDate[1]\n      }\n      businessReportList(this.reportParams).then(res => {\n        console.info('报表：', res)\n        this.tableList.data = res.rows\n        this.tableList.total = res.total\n      }).finally(() => {\n        this.reportStatisticsQueryLoading = false\n      })\n    },\n    //重置按钮操作\n    resetQuery() {\n      this.resetForm(\"reportForm\");\n      this.reportParams.billingDate = null\n      this.reportParams.startDate = null\n      this.reportParams.endDate = null\n      this.handleQuery();\n    },\n    //搜索按钮操作\n    handleQuery() {\n      this.reportParams.pageNum = 1;\n      this.getList();\n    },\n    //下载\n    downloadReport(row) {\n      if (!row.reportDataPath) {\n        this.$message.error('文件路径失效')\n        return\n      }\n      let paths = row.reportDataPath.split('/')\n      downloadFileByPathApi(row.reportDataPath).then(res => {\n        downloadFileHandler(res, paths[paths.length - 1])\n      })\n    },\n    //删除\n    deleteReport(row) {\n      let $this = this\n      this.$modal.confirm('是否确认删除？')\n        .then(function() {\n          businessReportDelete(row.id).then(res => {\n            $this.$message.success('删除成功')\n            $this.handleQuery()\n          })\n        }).catch(e => console.info(e))\n    },\n    //导出\n    exportReport() {\n      this.download('huida-reinsurance/reinsurance/businessReport/export', {\n        ...this.queryParams\n      }, `业务报表统计记录_${new Date().getTime()}.xlsx`)\n    }\n  }\n}\n</script>\n<style scoped>\n::v-deep .el-upload {\n  width: 100%;\n  padding-left: 20px;\n  padding-right: 20px;\n}\n::v-deep .el-upload-dragger {\n  width: 100%;\n}\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  padding: 20px 20px 0 0;\n}\n</style>\n"]}]}