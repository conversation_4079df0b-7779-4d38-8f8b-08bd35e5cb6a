{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\rate\\index.vue?vue&type=template&id=265a4d05", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\rate\\index.vue", "mtime": 1722240766000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750743151151}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750743149606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}