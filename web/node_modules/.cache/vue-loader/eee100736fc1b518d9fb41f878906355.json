{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\git\\huida-reinsurance\\web\\src\\components\\Formula\\editTiny.vue?vue&type=style&index=1&id=8dda4d3a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\components\\Formula\\editTiny.vue", "mtime": 1722240766000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750743146578}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750743151034}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750743148205}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750743145152}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750743149606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgo6OnYtZGVlcC5lbC10cmVlewogIC5lbC10cmVlLW5vZGV7CiAgICAuZWwtdHJlZS1ub2RlX19jb250ZW50ewogICAgICBoZWlnaHQ6IDMwcHg7CiAgICAgIGxpbmUtaGVpZ2h0OiAzMHB4OwogICAgfQogIH0KfQouY3VzdG9tLXRyZWUtbm9kZXsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IDMwcHg7CiAgbGluZS1oZWlnaHQ6IDMwcHg7CiAgLy8gZGlzcGxheTogZmxleDsKICAvLyBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgLy8gYWxpZ24taXRlbXM6IGNlbnRlcjsKICAubm9kZXsKICAgIHdpZHRoOiAzNjJweDsKICAgIG92ZXJmbG93OiBoaWRkZW47ICAgICAgIC8v6L+Z5Liq5piv6K6+572u6ZqQ6JeP55qE44CC6L+Y5pyJ5YW25LuW55qE77yM5L6L5aaCc2Nyb2xs77yM5piv6LaF5Ye65Zu65a6a6ZW/5bqm77yM5bqV6YOo5pi+56S65rua5Yqo5p2h55qE44CCCiAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsgICAvL+i/meS4quWwseaYr+iuvue9ruebtOaOpemakOiXj+aOieaWh+Wtl++8jOi/mOaYr+aYvuekui4uLueahOOAguW9k+WJjeaYr+aYvuekuuecgeeVpeWPt+OAguebtOaOpeecgeeVpeaYr2NsaXAKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsgICAgIC8v5qC55o2u5LiN5ZCM5qCH562+ZGlzcGxheeWAvO+8jOacieeahOS4jeeUqOWKoOOAggogIH0KICAuZGl5ewogICAgZmxvYXQ6IHJpZ2h0OwogIH0KfQoucGFyYW1zLWNvbmYgewogIG1hcmdpbi10b3A6IDEwcHg7CiAgLmRlc2MgewogICAgLmNvbnRlbnQgewogICAgICB0ZXh0LWFsaWduOiBsZWZ0OwogICAgICA6OnYtZGVlcCB1bCB7CiAgICAgICAgLmluZm8gewogICAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDsKICAgICAgICAgIGNvbG9yOiAjNGE1MzhhOwogICAgICAgIH0KICAgICAgICAuZXhhbXBsZSB7CiAgICAgICAgICBmb250LXdlaWdodDogNzAwOwogICAgICAgICAgLmZ1bmMgewogICAgICAgICAgICBjb2xvcjogIzg3MWFiMzsKICAgICAgICAgIH0KICAgICAgICAgIC5wYXJhbSB7CiAgICAgICAgICAgIGJhY2tncm91bmQ6ICMxZTg3YjY7CiAgICAgICAgICAgIGNvbG9yOiAjZmZmOwogICAgICAgICAgICBwYWRkaW5nOiAzcHggNXB4OwogICAgICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7CiAgICAgICAgICAgIG1hcmdpbjogMHB4IDVweDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9CiAgLmNvbmYtY29udGFpbmVyIHsKICAgIGhlaWdodDogNDAwcHg7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgYm9yZGVyOiAxcHggc29saWQgI2U5ZTllOTsKICAgIGJvcmRlci1yYWRpdXM6IDZweDsKICAgIC50aXRsZVQgewogICAgICBwYWRkaW5nOiAxMHB4IDhweDsKICAgICAgdGV4dC1hbGlnbjogbGVmdDsKICAgICAgZm9udC13ZWlnaHQ6IDcwMDsKICAgICAgY29sb3I6ICMwMDA7CiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllOWU5OwogICAgfQogICAgLmNvbnRlbnQgewogICAgICBoZWlnaHQ6IDgwJTsKICAgICAgb3ZlcmZsb3c6IGF1dG87CiAgICAgIC5pdGVtIHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgbWFyZ2luOiA4cHggMDsKICAgICAgICBwYWRkaW5nOiAwIDhweDsKCiAgICAgICAgLm5hbWUgewogICAgICAgICAgY29sb3I6ICMwMDA7CiAgICAgICAgICBmb250LXdlaWdodDogNzAwOwogICAgICAgIH0KICAgICAgICAudHlwZSB7CiAgICAgICAgICBtaW4td2lkdGg6IDUwcHg7CiAgICAgICAgICBwYWRkaW5nOiAxcHggNXB4OwogICAgICAgICAgYm9yZGVyLXJhZGl1czogNXB4OwogICAgICAgICAgY29sb3I6ICNmZmY7CiAgICAgICAgICBtYXJnaW4tbGVmdDogYXV0bzsKICAgICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQogIC50aW55bWNlLWJveHsKICAgIDo6di1kZWVwLnRveC1lZGl0b3ItaGVhZGVyewogICAgICBib3gtc2hhZG93Om5vbmUKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["editTiny.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2eA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "editTiny.vue", "sourceRoot": "src/components/Formula", "sourcesContent": ["<template>\n<el-dialog :visible.sync=\"tinymceDiaShow\" :title=\"title\" @close=\"close\" custom-class=\"tiny-dialog\" width=\"1200px\" append-to-body>\n  <div style=\"text-align:right;margin-bottom: 10px;\">\n    <el-button @click=\"clear\" size=\"mini\" :disabled=\"disabled\" type=\"warning\">清空公式内容</el-button>\n    <el-button size=\"mini\" type=\"primary\" :disabled=\"disabled\" @click=\"confirm\">确定</el-button>\n  </div>\n  <slot></slot>\n  <div v-if=\"isShowRule\">\n    <div class=\"tinymce-box\">\n      <Editor\n        v-if=\"tinymceDiaShow\"\n        id=\"myedit\"\n        v-model=\"myValue\"\n        tag-name=\"div\"\n        :init=\"init\"\n        :disabled=\"disabled\"\n        @click=\"onClick\">\n      </Editor>\n      <!-- 公式：{{formula}} -->\n      <div class=\"params-conf\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"conf-container\">\n              <div class=\"titleT\">\n                <span style=\"margin-right :10px;\">参数列表</span>\n                <!-- <el-radio-group v-model=\"paramsType\">\n                  <el-radio\n                    v-for=\"i in paramsListT\"\n                    :key=\"i.type\"\n                    :label=\"i.type\">{{i.name}} \n                  </el-radio>\n                </el-radio-group> -->\n              </div>\n              <el-input\n                placeholder=\"输入关键字进行过滤\"\n                v-model=\"filterText\">\n              </el-input>\n              <div class=\"content\" style=\"height:80%\" v-for=\"D in paramsListT\" :key=\"D.type+'_DIV'\" v-show=\"D.type == paramsType\">\n                <el-tree :data=\"D.treeData\" :ref=\"'eltree_'+D.type\" :filter-node-method=\"filterNode\" @node-click=\"handleNodeClick\">\n                   <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\n                    <el-tooltip v-if=\"node.level == 3\" effect=\"dark\" :content=\"data[D.props.value]+'-'+data[D.props.label]\" placement=\"top\">\n                      <span class=\"node\">{{ data[D.props.label] }}</span>\n                    </el-tooltip>\n                    <span v-else >{{ data[D.props.label] }}</span>\n                    <span v-if=\"D.nodeDiyContent.type == 'SELECT'\" class=\"diy\">\n                      <el-select v-model=\"data.nodeDiyValue\" value-key=\"value\" @change=\"val=>{nodeDiyChange(val,data,node)}\" placeholder=\"取数规则\" size=\"mini\" style=\"width:130px\">\n                        <el-option :label=\"n.label\" :value=\"n\" v-for=\"n in D.nodeDiyContent.options\" :key=\"n.value\"></el-option>\n                      </el-select>\n                    </span>\n                  </span>\n                </el-tree>\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"conf-container\">\n              <div class=\"titleT\">运算符与运算逻辑列表</div>\n              <div class=\"content\" style=\"height:90%\">\n                <div  v-for=\"(group,group_idx) in funcList\" :key=\"group_idx\">\n                  <div class=\"item\" @mouseenter=\"itemMouseenter(i)\" @mouseleave=\"itemMouseleave\"\n                    v-for=\"(i,idx) in group.children\" :key=\"idx\" @click=\"addTag(i,i.type)\">\n                    <div class=\"name\">{{i.name}}</div>\n                    <div class=\"type\" :style=\"typeColors.find(x=>x.name==i.dataType)?typeColors.find(x=>x.name==i.dataType).typeStyle:''\">\n                      {{i.dataTypeName}}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"6\">\n            <div class=\"conf-container desc\">\n              <div class=\"title\">说明</div>\n              <div class=\"content\">\n                <div v-html=\"descContent\"></div>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n    </div>\n  </div>\n</el-dialog>\n\n  \n</template>\n\n<script>\nimport confContent from \"./conf\";\n// 文档 http://tinymce.ax-z.cn/\n// 引入组件\nimport tinymce from 'tinymce/tinymce' // tinymce默认hidden，不引入不显示\nimport Editor from '@tinymce/tinymce-vue'\nimport \"tinymce/themes/silver\"; //编辑器主题，不引入则报错\nimport \"tinymce/icons/default\"; //引入编辑器图标icon，不引入则不显示对应图标\n// 引入富文本编辑器主题的js和css\n// import 'tinymce/skins/content/default/content.css'\n// import 'tinymce/themes/silver/theme.min.js'\n// import 'tinymce/icons/default/icons' // 解决了icons.js 报错Unexpected token '<'\n\n// 编辑器插件plugins\n// 更多插件参考：https://www.tiny.cloud/docs/plugins/\nimport 'tinymce/plugins/image'// 插入上传图片插件\nimport 'tinymce/plugins/media'// 插入视频插件\nimport 'tinymce/plugins/table'// 插入表格插件\nimport 'tinymce/plugins/lists'// 列表插件\nimport 'tinymce/plugins/wordcount'// 字数统计插件\nimport 'tinymce/plugins/link'\nimport 'tinymce/plugins/code'\nimport 'tinymce/plugins/preview'\nimport 'tinymce/plugins/fullscreen'\nimport 'tinymce/plugins/help'\nexport default {\n  components: {\n    Editor\n  },\n  name: 'Tinymce',\n  props: {\n    title: {\n      type: String,\n      default: \"编辑公式\"\n    },\n    isShowRule: {\n      type: Boolean,\n      default: true\n    },\n    // tree_data:{\n    //   type:Array,\n    //   default:function() {\n    //     return []\n    //   }\n    // },\n    // props:{\n    //   type:Object,\n    //   default:function(){\n    //     return{\n    //       //显示定义参数\n    //       label:'label',\n    //       value:'value',\n    //       //数据处理定义参数\n    //       // children:'children',\n    //       id:'id',\n    //       parentId:'parentId',\n    //     }\n    //   }\n    // },\n    // //树节点自定义内容\n    // nodeDiyContent:{\n    //   type:Object,\n    //   default:function(){\n    //     return{\n    //     }\n    //   }\n    // },\n    paramsList:{\n      type:Array,\n      default:function() {\n        return [\n          {\n            type:'', //数据类型 科目/计量.... \n            name:'',\n            props:{\n              label:'', //树形结构显示字段\n              value:'',  //树形结构值字段\n              id:'',//非树形结构数据不传id\n              parentId:'',\n            },\n            treeDataSource:[],//原始数据\n            nodeDiyContent:{  // 树形自定义内容\n              type:'SELECT', // 树形自定义内容类型\n              options:[  // 树形自定义内容数据\n              ]\n            }\n          },\n        ]\n      }\n    },\n    // 默认的富文本内容\n    value: {\n      type: String,\n      default: ''\n    },\n    // 基本路径，默认为空根目录，如果你的项目发布后的地址为目录形式，\n    // 即abc.com/tinymce，baseUrl需要配置成tinymce，不然发布后资源会找不到\n    baseUrl: {\n      type: String,\n      default: window.location.origin ? window.location.origin : ''\n    },\n    //显示\n    tinymceDia: {\n      type: Boolean,\n      default: false\n    },\n    plugins: {\n      type: [String, Array],\n      default: \"\",\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    toolbar: {\n      type: [String, Array],\n      default: \n       \"fullscreen undo redo restoredraft | cut copy paste pastetext | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent \",\n    }\n  },\n  data () {\n    return {\n      paramsType:'',\n      filterText: '',\n      tinymceDiaShow:false,\n      treeData:[],\n      init: {\n        language_url: `${this.baseUrl}${process.env.NODE_ENV=='development'?'':'/web'}/tinymce/langs/zh_CN.js`,\n        language: 'zh_CN',\n        skin_url: `${this.baseUrl}${process.env.NODE_ENV=='development'?'':'/web'}/tinymce/skins/ui/oxide`,\n        // skin_url: 'tinymce/skins/ui/oxide-dark', // 暗色系\n        convert_urls: false,\n        height: 200,\n        // content_css（为编辑区指定css文件）,加上就不显示字数统计了\n        // content_css: `${this.baseUrl}tinymce/skins/content/default/content.css`,\n        // （指定需加载的插件）\n        plugins: this.plugins,\n        toolbar: false, // （自定义工具栏）\n        menubar: false,\n        statusbar: false, // 底部的状态栏\n        // menubar: 'file edit insert view format table tools help', // （1级菜单）最上方的菜单\n        branding: false, // （隐藏右下角技术支持）水印“Powered by TinyMCE”\n        // 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，\n        // 如需ajax上传可参考https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_handler\n        images_upload_handler: (blobInfo, success, failure) => {\n          const img = 'data:image/jpeg;base64,' + blobInfo.base64()\n          success(img)\n          console.log(failure)\n        }\n      },\n      myValue: this.content,\n      formula:'',\n      typeColors:[\n        {\n          name: \"string\",\n          typeStyle: { background: \"#3e5ca0\", color: \"#fff\" },\n          viewStyle: {\n            background: \"#1e87b6\",\n            color: \"#fff\",\n            padding: \"3px 5px\",\n            borderRadius: \"3px\",\n          },\n        },\n        {\n          name: \"date\",\n          typeStyle: { background: \"#3e5ca0\", color: \"#fff\" },\n          viewStyle: {\n            background: \"#1e87b6\",\n            color: \"#fff\",\n            padding: \"3px 5px\",\n            borderRadius: \"3px\",\n          },\n        },\n        {\n          name: \"dateTime\",\n          typeStyle: { background: \"#3e5ca0\", color: \"#fff\" },\n          viewStyle: {\n            background: \"#1e87b6\",\n            color: \"#fff\",\n            padding: \"3px 5px\",\n            borderRadius: \"3px\",\n          },\n        },\n        {\n          name: \"boolean\",\n          typeStyle: { background: \"#3e5ca0\", color: \"#fff\" },\n          viewStyle: {\n            background: \"#1e87b6\",\n            color: \"#fff\",\n            padding: \"3px 5px\",\n            borderRadius: \"3px\",\n          },\n        },\n        {\n          name: \"number\",\n          typeStyle: { background: \"#ff7519\", color: \"#fff\" },\n          viewStyle: {\n            background: \"#1e87b6\",\n            color: \"#fff\",\n            padding: \"3px 5px\",\n            borderRadius: \"3px\",\n          },\n        },\n        {\n          name: \"func\",\n          typeStyle: { background: \"#3e5ca0\", color: \"#fff\" },\n          viewStyle: {\n            color: \"#871ab3\",\n            padding: \"3px 5px\",\n          },\n        },\n      ],\n      paramsListT:[],\n      fieldCurCollapse:'',\n      funcCurCollapse:'',\n      descContent:'',\n      fieldList:confContent.fieldList,\n      funcList:confContent.funcList,\n      viewStyle:'margin: 0px 5px 2px 5px; display: inline-block; background: #1e87b6; color: #fff; padding: 3px 5px; border-radius: 3px;'\n    }\n  },\n  mounted () {\n    tinymce.init({})\n    console.log(this.init.language_url)\n  },\n  methods: {\n    filterNode(value, data) {\n      if (!value) return true;\n      let props = this.paramsListT.find(\n        (item) => item.type === this.paramsType\n      ).props\n      return data[props.label].indexOf(value) !== -1;\n    },\n    // 添加相关的事件，可用的事件参照文档=> https://github.com/tinymce/tinymce-vue => All available events\n    // 需要什么事件可以自己增加\n    onClick (e) {\n      this.$emit('onClick', e, tinymce)\n    },\n    // 可以添加一些自己的自定义事件，如清空内容\n    clear () {\n      this.myValue = ''\n    },\n    close(){\n      this.$emit('close',false)\n    },\n    handleNodeClick(data){\n      if(this.disabled){\n        return\n      }\n      data.dataType = 'string'\n      let nodeDiyContent = this.paramsListT.find(\n        (item) => item.type === this.paramsType\n      )?.nodeDiyContent||{}\n      if(nodeDiyContent.type&& !data.nodeDiyValue){\n        this.$message.error('请选择取数规则')\n        return\n      }\n      this.addTag(data,'field')\n    },\n    confirm(){\n      let calculationFormulaParams=this.formula\n      let calculationFormulaText=this.myValue\n      this.paramsListT.forEach(i=>{\n        i.treeDataSource.forEach(s=>{\n           if(i.nodeDiyContent.type&&i.nodeDiyContent.options.length){\n            i.nodeDiyContent.options.forEach(n=>{\n              calculationFormulaParams = calculationFormulaParams.replace(\n                new RegExp(`data-value=\"${s[i.props.value]}-${s[i.props.label]}.${n.label}\">${s[i.props.label]}.${n.label}`,\"g\"),\n                `${s[i.props.value]}`\n              )\n            })\n          }else{\n            if(s[i.props.label]){\n              let label = s[i.props.label].replace(/\\(/g,'\\\\(').replace(/\\)/g,'\\\\)')\n              calculationFormulaParams = calculationFormulaParams.replace(\n                new RegExp(`data-value=\"${s[i.props.value]}-${label}\">${label}`,\"g\"),\n                `${s[i.props.value]}`\n              )\n            }\n          }\n        })\n      })\n\n      calculationFormulaText = calculationFormulaText&&calculationFormulaText.replace(new RegExp(`${this.viewStyle}`,\"g\"),'viewStyle')\n      this.$emit('confirm',{\n        calculationFormula:calculationFormulaParams, // 接口所需公式\n        calculationUser:tinymce.editors[\"myedit\"].getContent({'format':'text'}), //列表用户对展示公式\n        calculationFormulaText:calculationFormulaText // 富文本回显字符串\n      })\n    },\n    nodeDiyChange(val,data,node){\n    },\n    addTag(data, type){\n      if(this.disabled){\n        return\n      }\n      // let viewStyle =\n      //   type == \"field\"\n      //     ? this.typeColors.find((x) => x.name == data.dataType)?.viewStyle\n      //     : this.typeColors.find((x) => x.name == \"func\")?.viewStyle;\n      // let viewStyleStr = \"margin:0px 5px 2px 5px;display:inline-block;\";\n      // Object.keys(viewStyle).map((key) => {\n      //   let keyStr = key.replace(/([A-Z])/g, \"-$1\").toLowerCase();\n      //   viewStyleStr += `${keyStr}:${viewStyle[key]};`;\n      // });\n      let props = this.paramsListT.find(\n        (item) => item.type === this.paramsType\n      ).props\n      if (type == \"field\") {\n        console.log(data)\n        console.log(props)\n        tinymce.editors[\"myedit\"].insertContent(\n          `\n          <span style=\"${this.viewStyle}\" \n          data-value=\"${data[props.value]}-${data[props.label]}${data.nodeDiyValue?'.'+data.nodeDiyValue.label:''}\"\n          contenteditable=\"false\">${data[props.label]}${data.nodeDiyValue?'.'+data.nodeDiyValue.label:''}</span> \n          `\n        );\n      } else if (type == \"func\"){\n        tinymce.editors[\"myedit\"].insertContent(\n          ` ${data.name}() `\n        );\n      }else if (type == \"func-if\"){\n        tinymce.editors[\"myedit\"].insertContent(\n          ` if( ){ }else{ } `\n        );\n      }else if (type == \"else-if\"){\n        tinymce.editors[\"myedit\"].insertContent(\n          ` if(  ){  }else if(  ){  }else{  }`\n        );\n      }else{\n        tinymce.editors[\"myedit\"].insertContent(\n          ` ${data.name} `\n        );\n      }\n    },\n    itemMouseenter(item){\n      this.descContent = item.desc;\n    },\n    itemMouseleave(){\n      this.descContent = '';\n    },\n    initTreeData() {\n      this.paramsListT = JSON.parse(JSON.stringify(this.paramsList))\n      this.paramsListT.forEach(i=>{\n        if(i.props.id&&i.props.parentId){\n          i.treeData = this.handleTree(i.treeDataSource,i.props.id,i.props.parentId)\n        }else{\n          i.treeData = JSON.parse(JSON.stringify(i.treeDataSource))\n        }\n      })\n    }\n  },\n  watch: {\n    filterText(val) {\n      this.$refs[`eltree_${this.paramsType}`][0].filter(val);\n    },\n    value (newValue) {\n      if(!newValue){\n        this.clear()\n      }else{\n        this.myValue = this.value.replace(/(viewStyle)/g,this.viewStyle);\n      }\n      this.paramsListT = JSON.parse(JSON.stringify(this.paramsList))\n      this.paramsListT.forEach(i=>{\n        if(i.props.id&&i.props.parentId){\n          i.treeData = this.handleTree(i.treeDataSource,i.props.id,i.props.parentId)\n        }else{\n          i.treeData = JSON.parse(JSON.stringify(i.treeDataSource))\n        }\n      })\n    },\n    myValue (newValue) {\n      if(!newValue) return\n      this.formula = newValue\n      .replace(/(<span style=\"margin: 0px 5px 2px 5px; display: inline-block; color: #871ab3; padding: 3px 5px;\" contenteditable=\"false\">)/g,'')\n      .replace(/(<span style=\"margin: 0px 5px 2px 5px; display: inline-block; background: #1e87b6; color: #fff; padding: 3px 5px; border-radius: 3px;\" contenteditable=\"false\">)/g,'')\n      .replace(/(<span style=\"margin: 0px 5px 2px 5px; display: inline-block; background: #1e87b6; color: #fff; padding: 3px 5px; border-radius: 3px;\" contenteditable=\"false\")/g,'')\n      .replace(/(<\\/span>)/g,'')\n      .replace(/(<p>)/g,'')\n      .replace(/(<\\/p>)/g,'')\n      .replace(/(&nbsp;)/g,'')\n      .replace(/(&gt;)/g,'>')\n      .replace(/(&lt;)/g,'<')\n    },\n    tinymceDia(newV){\n      this.tinymceDiaShow = newV\n      this.paramsType = this.paramsList[0].type\n      if(newV){\n        this.initTreeData();\n      }\n    }\n  }\n}\n\n</script>\n<style lang=\"scss\" >\n.tiny-dialog{\n  .el-dialog__body{\n    padding-top: 10px 20px;\n  }\n}\n</style>\n<style lang=\"scss\"  scoped>\n::v-deep.el-tree{\n  .el-tree-node{\n    .el-tree-node__content{\n      height: 30px;\n      line-height: 30px;\n    }\n  }\n}\n.custom-tree-node{\n  width: 100%;\n  height: 30px;\n  line-height: 30px;\n  // display: flex;\n  // justify-content: space-between;\n  // align-items: center;\n  .node{\n    width: 362px;\n    overflow: hidden;       //这个是设置隐藏的。还有其他的，例如scroll，是超出固定长度，底部显示滚动条的。\n    text-overflow: ellipsis;   //这个就是设置直接隐藏掉文字，还是显示...的。当前是显示省略号。直接省略是clip\n    display: inline-block;     //根据不同标签display值，有的不用加。\n  }\n  .diy{\n    float: right;\n  }\n}\n.params-conf {\n  margin-top: 10px;\n  .desc {\n    .content {\n      text-align: left;\n      ::v-deep ul {\n        .info {\n          font-weight: 700;\n          color: #4a538a;\n        }\n        .example {\n          font-weight: 700;\n          .func {\n            color: #871ab3;\n          }\n          .param {\n            background: #1e87b6;\n            color: #fff;\n            padding: 3px 5px;\n            border-radius: 3px;\n            margin: 0px 5px;\n          }\n        }\n      }\n    }\n  }\n  .conf-container {\n    height: 400px;\n    overflow: hidden;\n    border: 1px solid #e9e9e9;\n    border-radius: 6px;\n    .titleT {\n      padding: 10px 8px;\n      text-align: left;\n      font-weight: 700;\n      color: #000;\n      border-bottom: 1px solid #e9e9e9;\n    }\n    .content {\n      height: 80%;\n      overflow: auto;\n      .item {\n        display: flex;\n        align-items: center;\n        margin: 8px 0;\n        padding: 0 8px;\n\n        .name {\n          color: #000;\n          font-weight: 700;\n        }\n        .type {\n          min-width: 50px;\n          padding: 1px 5px;\n          border-radius: 5px;\n          color: #fff;\n          margin-left: auto;\n          font-weight: 700;\n        }\n      }\n    }\n  }\n  .tinymce-box{\n    ::v-deep.tox-editor-header{\n      box-shadow:none\n    }\n  }\n}\n</style>\n"]}]}