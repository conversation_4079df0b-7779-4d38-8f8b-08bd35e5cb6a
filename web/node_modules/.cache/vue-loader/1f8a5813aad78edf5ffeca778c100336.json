{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\lightningFacultative\\index.vue?vue&type=style&index=0&id=9a21a0aa&scoped=true&lang=css", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\lightningFacultative\\index.vue", "mtime": 1722240766000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750743146578}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750743151034}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750743148205}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750743149606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCjo6di1kZWVwIC5kdWUtZGF0YSB7CiAgY29sb3I6IGRhcmtyZWQgIWltcG9ydGFudDsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwUA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/reinsurance/lightningFacultative", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"120px\">\n      <el-form-item label=\"被保险人客户号\" prop=\"insuredNo\">\n        <el-input v-model=\"queryParams.insuredNo\" placeholder=\"请输入\" clearable @keyup.enter.native=\"handleQuery\" />\n      </el-form-item>\n      <el-form-item label=\"被保人姓名\" prop=\"insuredName\">\n        <el-input v-model=\"queryParams.insuredName\" placeholder=\"请输入\" clearable @keyup.enter.native=\"handleQuery\" />\n      </el-form-item>\n<!--      <el-form-item label=\"保单号\" prop=\"contNo\">-->\n<!--        <el-input v-model=\"queryParams.contNo\" placeholder=\"请输入\" clearable @keyup.enter.native=\"handleQuery\" />-->\n<!--      </el-form-item>-->\n<!--      <el-form-item label=\"险种编码\" prop=\"riskCode\">-->\n<!--        <el-input v-model=\"queryParams.riskCode\" placeholder=\"请输入险种编码\" clearable @keyup.enter.native=\"handleQuery\" />-->\n<!--      </el-form-item>-->\n<!--      <el-form-item label=\"再保责任编码\" prop=\"liabilityCode\">-->\n<!--        <el-input v-model=\"queryParams.liabilityCode\" placeholder=\"请输入\" clearable @keyup.enter.native=\"handleQuery\" />-->\n<!--      </el-form-item>-->\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain size=\"mini\" @click=\"cedeoutTypeHandler(1, null)\" :disabled=\"multiple\">自留分保</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain size=\"mini\" @click=\"cedeoutTypeHandler(2, null)\" :disabled=\"multiple\">合同分保</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain size=\"mini\" @click=\"customProgrammeHandler\" :disabled=\"multiple\">自定义方案</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\"\n          @click=\"handleExport\" v-has-permi=\"['lightning:facultative:export']\">导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"facultativeList\" @selection-change=\"handleSelectionChange\"\n      :row-class-name=\"({row, rowIndex}) => row.dataStatus === 1 ? 'due-data' : ''\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" :selectable=\"selectable\" />\n      <el-table-column width=\"60\" align=\"center\" label=\"序号\">\n        <template slot-scope=\"scope\">\n          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}\n        </template>\n      </el-table-column>\n      <el-table-column label=\"被保险人客户号\" min-width=\"120\" align=\"center\" prop=\"insuredNo\" />\n      <el-table-column label=\"被保人姓名\" min-width=\"120\" align=\"center\" prop=\"insuredName\" />\n      <el-table-column label=\"保单号\" min-width=\"180\" align=\"center\" prop=\"contNo\" />\n      <el-table-column label=\"分保方式\" min-width=\"120\" align=\"center\" prop=\"cedeoutType\">\n        <template slot-scope=\"{ row }\">\n          <dict-tag :options=\"dict.type.cedeout_facultative_type\" :value=\"row.cedeoutType\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"险种编码\" min-width=\"220\" align=\"center\" prop=\"riskCode\" show-overflow-tooltip>\n        <template slot-scope=\"{ row }\">\n          <span style=\"display: flex;align-items: center;\">\n            {{`${row.riskCode}/`}}\n          <dict-tag :options=\"dict.type.core_insurance_type\" :value=\"row.riskCode\"/>\n          </span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"给付责任编码\" min-width=\"250\" align=\"center\" prop=\"getDutyCode\" show-overflow-tooltip>\n        <template slot-scope=\"{ row }\">\n          <span style=\"display: flex;align-items: center;\" v-if=\"row.getDutyCode\">\n            <span style=\"display: flex;align-items: center;\" v-for=\"(code, index) in row.getDutyCode.split(',')\">\n              <span style=\"display: flex;align-items: center;\">\n                {{`${code}/`}}\n                <dict-tag :options=\"dict.type.core_insurance_liability\" :value=\"code\"/>\n                <span v-if=\"index !== row.getDutyCode.split(',').length - 1\">，</span>\n              </span>\n            </span>\n          </span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"再保责任编码\" min-width=\"180\" align=\"center\" prop=\"liabilityCode\" show-overflow-tooltip>\n        <template slot-scope=\"{ row }\">\n          <span style=\"display: flex;align-items: center;justify-content: start;\">\n            {{`${row.liabilityCode}/`}}\n            <dict-tag :options=\"dict.type.risk_liability_reinsurance_duty\" :value=\"row.liabilityCode\"/>\n          </span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"再保方案编码\" min-width=\"200\" align=\"center\" prop=\"programmeCode\" show-overflow-tooltip>\n        <template slot-scope=\"{ row }\">\n          <span style=\"display: flex;align-items: center;\" v-if=\"row.programmeCode\">\n            <span style=\"display: flex;align-items: center;\" v-for=\"(code, index) in row.programmeCode.split(',')\">\n              <span style=\"display: flex;align-items: center;\">\n                {{`${code}/`}}\n                <dict-tag :options=\"dict.type.insurance_programme\" :value=\"code\"/>\n                <span v-if=\"index !== row.programmeCode.split(',').length - 1\">，</span>\n              </span>\n            </span>\n          </span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"核心临分结论\" min-width=\"200\" align=\"center\" prop=\"coreConclusion\" />\n      <el-table-column label=\"再保临分结论\" min-width=\"200\" align=\"center\" prop=\"reinsuranceConclusion\" />\n      <el-table-column label=\"数据标记\" min-width=\"100\" align=\"center\" prop=\"dataStatus\">\n        <template slot-scope=\"{ row }\">\n          <dict-tag :options=\"dict.type.cedeout_facultative_data_status\" :value=\"row.dataStatus\"/>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"EM加点\" align=\"center\" prop=\"em\" />\n<!--      <el-table-column label=\"计算标记\" align=\"center\" prop=\"calcStatus\" />-->\n<!--      <el-table-column label=\"临分确认书\" align=\"center\" prop=\"confirmingOrder\" />-->\n      <el-table-column label=\"操作\" width=\"200\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"{ row }\">\n          <el-button size=\"mini\" type=\"text\" @click=\"showConfirmation(row)\">临分确认书查看</el-button>\n          <el-button size=\"mini\" type=\"text\" v-has-permi=\"['lightning:facultative:edit']\" @click=\"handleUpdate(row)\" v-if=\"row.dataStatus !== 2\">修改</el-button>\n          <el-button size=\"mini\" type=\"text\" v-has-permi=\"['lightning:facultative:edit']\" @click=\"handleAdjustment(row)\" v-if=\"row.dataStatus === 2\">调整</el-button>\n          <el-button size=\"mini\" type=\"text\" @click=\"handlerInputCompleted(row.id)\" v-if=\"row.dataStatus !== 2\">录入完毕</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 自定义方案对话框 -->\n    <el-dialog :title=\"dialog.title\" :visible.sync=\"dialog.visible\" :close-on-click-modal=\"false\" :width=\"dialog.width\">\n      <component :is=\"dialog.componentsName\" v-if=\"dialog.visible\" :params=\"dialog.params\" @ok=\"dialogOk\" @cancel=\"dialogCancel\" />\n    </el-dialog>\n\n  </div>\n</template>\n<script>\nimport {\n  listFacultative, cedeoutTypeApi, inputCompleted, adjustmentFacultative\n} from \"@/api/reinsurance/lightningFacultative\";\nimport ProgrammeEdit from \"@/views/reinsurance/programme/programmeEdit.vue\";\nimport LightningFacultativeEdit from '@/views/reinsurance/lightningFacultative/edit.vue'\nimport {UPDATE} from \"@/utils/systemUtil\";\nimport FileList from \"@/components/FileList/index.vue\";\n\nexport default {\n  dicts: [\n    'core_insurance_type',\n    'risk_liability_reinsurance_duty',\n    'cedeout_facultative_data_status',\n    'insurance_programme',\n    'cedeout_facultative_type',\n    'core_insurance_liability'\n  ],\n  components: {\n    ProgrammeEdit,\n    LightningFacultativeEdit,\n    FileList\n  },\n  data() {\n    return {\n      dialog: {\n        title: '',\n        visible: false,\n        componentsName: null,\n        width: '1300px',\n        params: {}\n      },\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 临分数据表格数据\n      facultativeList: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        insuredNo: null,\n        insuredName: null,\n        contNo: null,\n        riskCode: null,\n        liabilityCode: null\n      },\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    //表格中的勾选是否可用\n    selectable(row, rowIndex) {\n      return row.dataStatus !== 2\n    },\n    //模态框确定\n    dialogOk(programmeCode) {\n      this.dialogCancel()\n      //有方案编码，是自定义方案操作需要绑定\n      if (programmeCode) {\n        //刷新字典\n        this.dict.reloadDict('insurance_programme')\n        //自定义方案操作\n        this.cedeoutType(3, programmeCode)\n      } else {\n        //刷新列表\n        this.getList()\n      }\n    },\n    //模态框取消\n    dialogCancel() {\n      this.dialog = {\n        title: '',\n        visible: false,\n        params: null,\n        componentsName: null,\n      }\n    },\n    /** 查询临分数据列表 */\n    getList() {\n      this.loading = true;\n      listFacultative(this.queryParams).then(response => {\n        this.facultativeList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.dialog = {\n        title: '修改临分数据',\n        visible: true,\n        width: '500px',\n        params: {...row, optionType: UPDATE},\n        componentsName: 'LightningFacultativeEdit',\n      }\n    },\n    //调整\n    handleAdjustment(row) {\n      let $this = this\n      this.$modal.confirm('是否确认调整，确认后数据状态将变更为待处理？')\n        .then(function() {\n          adjustmentFacultative(row).then(res => {\n            $this.$message.success('调整成功')\n            $this.getList()\n          })\n        }).catch(e => console.info(e))\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('/huida-reinsurance/lightning/facultative/export', {\n        ...this.queryParams\n      }, `facultative_${new Date().getTime()}.xlsx`)\n    },\n    //自定义方案模态框\n    customProgrammeHandler() {\n      this.dialog.title = '自定义再保方案'\n      this.dialog.visible = true\n      this.dialog.width = '1300px'\n      this.dialog.params = {optionType: 'add'}\n      this.dialog.componentsName = 'ProgrammeEdit'\n    },\n    cedeoutTypeHandler(type, programmeCode) {\n      let _this = this\n      this.$modal.confirm('是否确认？').then(function() {\n        _this.cedeoutType(type, programmeCode)\n      }).catch(() => {});\n    },\n    //自留分保/合同分保\n    cedeoutType(type, programmeCode) {\n      let data = {\n        type: type,\n        ids: this.ids,\n        programmeCode: programmeCode\n      }\n      this.loading = true\n      cedeoutTypeApi(data).then(res => {\n        this.$message.success('操作成功')\n        //刷新\n        this.handleQuery()\n      }).finally(() => {\n        this.loading = false\n      })\n    },\n    //录入完毕\n    handlerInputCompleted(id) {\n      this.$modal.confirm('是否确认？').then(function() {\n        return inputCompleted([id]);\n      }).then(() => {\n        this.$modal.msgSuccess(\"操作成功\");\n        this.getList();\n      }).catch(() => {})\n    },\n    //临分确认书查看\n    showConfirmation(row) {\n      this.dialog = {\n        title: '查看临分确认书',\n        visible: true,\n        width: '650px',\n        params: {fileType: 'CFC', businessCode: `${row.contNo}_${row.riskCode}`},\n        componentsName: 'FileList',\n      }\n    }\n  },\n}\n</script>\n<style scoped>\n::v-deep .due-data {\n  color: darkred !important;\n}\n</style>\n"]}]}