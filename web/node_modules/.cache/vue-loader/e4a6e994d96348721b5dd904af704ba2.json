{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\businessReport\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\businessReport\\index.vue", "mtime": 1722240766000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750743148245}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750743149606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgYnVzaW5lc3NSZXBvcnREZWxldGUsCiAgYnVzaW5lc3NSZXBvcnRMaXN0LAogIGJ1c2luZXNzUmVwb3J0U3RhdGlzdGljcwp9IGZyb20gIkAvYXBpL3JlaW5zdXJhbmNlL2J1c2luZXNzUmVwb3J0IjsKaW1wb3J0IHtkb3dubG9hZEZpbGVCeVBhdGhBcGl9IGZyb20gIkAvYXBpL3JlaW5zdXJhbmNlL2ZpbGUiOwppbXBvcnQge2Rvd25sb2FkRmlsZUhhbmRsZXJ9IGZyb20gIkAvYXBpL3Rvb2wvZmlsZSI7CgpleHBvcnQgZGVmYXVsdCB7CiAgZGljdHM6IFsncmVwb3J0X3R5cGUnLCAnYnVzaW5lc3NfcmVwb3J0X3N0YXR1cycsICdzeXNfdXNlciddLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvL+aYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvL+aKpeihqOafpeivomxvYWRpbmcKICAgICAgcmVwb3J0U3RhdGlzdGljc1F1ZXJ5TG9hZGluZzogZmFsc2UsCiAgICAgIC8v5oql6KGo57uf6K6h5Y+C5pWwCiAgICAgIHJlcG9ydFBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHJlcG9ydFR5cGU6IG51bGwsCiAgICAgICAgYmlsbGluZ0RhdGU6IG51bGwKICAgICAgfSwKICAgICAgLy/miqXooajnu5/orqFsb2FkaW5nCiAgICAgIHJlcG9ydFN0YXRpc3RpY3NMb2FkaW5nOiBmYWxzZSwKICAgICAgLy/ooajmoLzmlbDmja4KICAgICAgdGFibGVMaXN0OiB7CiAgICAgICAgZGF0YTogW10sCiAgICAgICAgdG90YWw6IDAKICAgICAgfQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+aKpeihqOe7n+iuoQogICAgcmVwb3J0U3RhdGlzdGljcygpIHsKICAgICAgaWYgKCF0aGlzLnJlcG9ydFBhcmFtcy5iaWxsaW5nRGF0ZSB8fCB0aGlzLnJlcG9ydFBhcmFtcy5iaWxsaW5nRGF0ZS5sZW5ndGggIT09IDIpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7fpgInmi6notKbljZXml6XmnJ8nKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIGlmICghdGhpcy5yZXBvcnRQYXJhbXMucmVwb3J0VHlwZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqeaKpeihqOexu+WeiycpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgdGhpcy5yZXBvcnRTdGF0aXN0aWNzTG9hZGluZyA9IHRydWUKICAgICAgdGhpcy5yZXBvcnRQYXJhbXMuc3RhcnREYXRlID0gdGhpcy5yZXBvcnRQYXJhbXMuYmlsbGluZ0RhdGVbMF0KICAgICAgdGhpcy5yZXBvcnRQYXJhbXMuZW5kRGF0ZSA9IHRoaXMucmVwb3J0UGFyYW1zLmJpbGxpbmdEYXRlWzFdCiAgICAgIGJ1c2luZXNzUmVwb3J0U3RhdGlzdGljcyh0aGlzLnJlcG9ydFBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pON5L2c5oiQ5Yqf77yM6K+36ICQ5b+D562J5b6FJykKICAgICAgICAvL+e7n+iuoeWujOaIkOWQju+8jOa4heepuuafpeivouadoeS7tgogICAgICAgIHRoaXMucmVzZXRRdWVyeSgpCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gewogICAgICAgIHRoaXMucmVwb3J0U3RhdGlzdGljc0xvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKICAgIC8v5oql6KGo6K6w5b2V5p+l6K+iCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLnJlcG9ydFN0YXRpc3RpY3NRdWVyeUxvYWRpbmcgPSB0cnVlCiAgICAgIGlmICh0aGlzLnJlcG9ydFBhcmFtcy5iaWxsaW5nRGF0ZSAmJiB0aGlzLnJlcG9ydFBhcmFtcy5iaWxsaW5nRGF0ZS5sZW5ndGggPT09IDIpIHsKICAgICAgICB0aGlzLnJlcG9ydFBhcmFtcy5zdGFydERhdGUgPSB0aGlzLnJlcG9ydFBhcmFtcy5iaWxsaW5nRGF0ZVswXQogICAgICAgIHRoaXMucmVwb3J0UGFyYW1zLmVuZERhdGUgPSB0aGlzLnJlcG9ydFBhcmFtcy5iaWxsaW5nRGF0ZVsxXQogICAgICB9CiAgICAgIGJ1c2luZXNzUmVwb3J0TGlzdCh0aGlzLnJlcG9ydFBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgIGNvbnNvbGUuaW5mbygn5oql6KGo77yaJywgcmVzKQogICAgICAgIHRoaXMudGFibGVMaXN0LmRhdGEgPSByZXMucm93cwogICAgICAgIHRoaXMudGFibGVMaXN0LnRvdGFsID0gcmVzLnRvdGFsCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gewogICAgICAgIHRoaXMucmVwb3J0U3RhdGlzdGljc1F1ZXJ5TG9hZGluZyA9IGZhbHNlCiAgICAgIH0pCiAgICB9LAogICAgLy/ph43nva7mjInpkq7mk43kvZwKICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJyZXBvcnRGb3JtIik7CiAgICAgIHRoaXMucmVwb3J0UGFyYW1zLmJpbGxpbmdEYXRlID0gbnVsbAogICAgICB0aGlzLnJlcG9ydFBhcmFtcy5zdGFydERhdGUgPSBudWxsCiAgICAgIHRoaXMucmVwb3J0UGFyYW1zLmVuZERhdGUgPSBudWxsCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvL+aQnOe0ouaMiemSruaTjeS9nAogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucmVwb3J0UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvL+S4i+i9vQogICAgZG93bmxvYWRSZXBvcnQocm93KSB7CiAgICAgIGlmICghcm93LnJlcG9ydERhdGFQYXRoKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu26Lev5b6E5aSx5pWIJykKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICBsZXQgcGF0aHMgPSByb3cucmVwb3J0RGF0YVBhdGguc3BsaXQoJy8nKQogICAgICBkb3dubG9hZEZpbGVCeVBhdGhBcGkocm93LnJlcG9ydERhdGFQYXRoKS50aGVuKHJlcyA9PiB7CiAgICAgICAgZG93bmxvYWRGaWxlSGFuZGxlcihyZXMsIHBhdGhzW3BhdGhzLmxlbmd0aCAtIDFdKQogICAgICB9KQogICAgfSwKICAgIC8v5Yig6ZmkCiAgICBkZWxldGVSZXBvcnQocm93KSB7CiAgICAgIGxldCAkdGhpcyA9IHRoaXMKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk77yfJykKICAgICAgICAudGhlbihmdW5jdGlvbigpIHsKICAgICAgICAgIGJ1c2luZXNzUmVwb3J0RGVsZXRlKHJvdy5pZCkudGhlbihyZXMgPT4gewogICAgICAgICAgICAkdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKQogICAgICAgICAgICAkdGhpcy5oYW5kbGVRdWVyeSgpCiAgICAgICAgICB9KQogICAgICAgIH0pLmNhdGNoKGUgPT4gY29uc29sZS5pbmZvKGUpKQogICAgfSwKICAgIC8v5a+85Ye6CiAgICBleHBvcnRSZXBvcnQoKSB7CiAgICAgIHRoaXMuZG93bmxvYWQoJ2h1aWRhLXJlaW5zdXJhbmNlL3JlaW5zdXJhbmNlL2J1c2luZXNzUmVwb3J0L2V4cG9ydCcsIHsKICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zCiAgICAgIH0sIGDkuJrliqHmiqXooajnu5/orqHorrDlvZVfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiFA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/reinsurance/businessReport", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-card header=\"报表生成\">\n      <el-form :model=\"reportParams\" ref=\"reportForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"75px\">\n        <el-form-item label=\"报表类型\" prop=\"reportType\">\n          <el-select v-model=\"reportParams.reportType\" placeholder=\"请选择报表类型\" filterable clearable>\n            <el-option v-for=\"item in dict.type.report_type\"\n              :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"账单日期\" prop=\"billingDate\">\n          <el-date-picker v-model=\"reportParams.billingDate\" type=\"daterange\" range-separator=\"至\"\n            start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" value-format=\"yyyy-MM-dd\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"getList\">查询</el-button>\n          <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n      <el-row :gutter=\"10\" class=\"mb8\">\n        <el-col :span=\"1.5\">\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"reportStatistics\"\n            v-has-permi=\"['reinsurance:businessReport:reportStatistics']\" :loading=\"reportStatisticsLoading\">报表统计</el-button>\n        </el-col>\n        <el-col :span=\"1.5\">\n          <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\"\n            v-has-permi=\"['reinsurance:businessReport:export']\" @click=\"exportReport\">导出</el-button>\n        </el-col>\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n      </el-row>\n\n      <el-table v-loading=\"reportStatisticsQueryLoading\" :data=\"tableList.data\">\n        <el-table-column align=\"center\" label=\"序号\" width=\"60\">\n          <template slot-scope=\"scope\">\n            {{ (reportParams.pageNum - 1) * reportParams.pageSize + scope.$index + 1 }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"报表类型\" align=\"center\" prop=\"reportType\" width=\"150\">\n          <template slot-scope=\"{row}\">\n            <dict-tag :options=\"dict.type.report_type\" :value=\"row.reportType\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"报表名称\" align=\"center\" prop=\"reportName\" min-width=\"260\" show-overflow-tooltip />\n        <el-table-column label=\"账单业务时间\" align=\"center\" prop=\"startDate\" width=\"220\">\n          <template slot-scope=\"{row}\">\n            {{`${row.startDate} ~ ${row.endDate}`}}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"100\">\n          <template slot-scope=\"{row}\">\n            <dict-tag :options=\"dict.type.business_report_status\" :value=\"row.status\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作人\" align=\"center\" prop=\"status\" width=\"150\">\n          <template slot-scope=\"{row}\">\n            <dict-tag :options=\"dict.type.sys_user\" :value=\"row.createBy\"/>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\" />\n        <el-table-column label=\"修改时间\" align=\"center\" prop=\"updateTime\" width=\"160\" />\n        <el-table-column label=\"操作\" align=\"center\" width=\"180\" fixed=\"right\">\n          <template slot-scope=\"{row}\">\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-download\" v-if=\"row.status === 0\" @click=\"downloadReport(row)\">下载</el-button>\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"deleteReport(row)\">删除</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"tableList.total > 0\"\n        :total=\"tableList.total\"\n        :page.sync=\"reportParams.pageNum\"\n        :limit.sync=\"reportParams.pageSize\"\n        @pagination=\"getList\"\n      />\n    </el-card>\n  </div>\n</template>\n<script>\nimport {\n  businessReportDelete,\n  businessReportList,\n  businessReportStatistics\n} from \"@/api/reinsurance/businessReport\";\nimport {downloadFileByPathApi} from \"@/api/reinsurance/file\";\nimport {downloadFileHandler} from \"@/api/tool/file\";\n\nexport default {\n  dicts: ['report_type', 'business_report_status', 'sys_user'],\n  data() {\n    return {\n      //显示搜索条件\n      showSearch: true,\n      //报表查询loading\n      reportStatisticsQueryLoading: false,\n      //报表统计参数\n      reportParams: {\n        pageNum: 1,\n        pageSize: 10,\n        reportType: null,\n        billingDate: null\n      },\n      //报表统计loading\n      reportStatisticsLoading: false,\n      //表格数据\n      tableList: {\n        data: [],\n        total: 0\n      }\n    }\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    //报表统计\n    reportStatistics() {\n      if (!this.reportParams.billingDate || this.reportParams.billingDate.length !== 2) {\n        this.$message.error('请选择账单日期')\n        return\n      }\n      if (!this.reportParams.reportType) {\n        this.$message.error('请选择报表类型')\n        return\n      }\n      this.reportStatisticsLoading = true\n      this.reportParams.startDate = this.reportParams.billingDate[0]\n      this.reportParams.endDate = this.reportParams.billingDate[1]\n      businessReportStatistics(this.reportParams).then(res => {\n        this.$message.success('操作成功，请耐心等待')\n        //统计完成后，清空查询条件\n        this.resetQuery()\n      }).finally(() => {\n        this.reportStatisticsLoading = false\n      })\n    },\n    //报表记录查询\n    getList() {\n      this.reportStatisticsQueryLoading = true\n      if (this.reportParams.billingDate && this.reportParams.billingDate.length === 2) {\n        this.reportParams.startDate = this.reportParams.billingDate[0]\n        this.reportParams.endDate = this.reportParams.billingDate[1]\n      }\n      businessReportList(this.reportParams).then(res => {\n        console.info('报表：', res)\n        this.tableList.data = res.rows\n        this.tableList.total = res.total\n      }).finally(() => {\n        this.reportStatisticsQueryLoading = false\n      })\n    },\n    //重置按钮操作\n    resetQuery() {\n      this.resetForm(\"reportForm\");\n      this.reportParams.billingDate = null\n      this.reportParams.startDate = null\n      this.reportParams.endDate = null\n      this.handleQuery();\n    },\n    //搜索按钮操作\n    handleQuery() {\n      this.reportParams.pageNum = 1;\n      this.getList();\n    },\n    //下载\n    downloadReport(row) {\n      if (!row.reportDataPath) {\n        this.$message.error('文件路径失效')\n        return\n      }\n      let paths = row.reportDataPath.split('/')\n      downloadFileByPathApi(row.reportDataPath).then(res => {\n        downloadFileHandler(res, paths[paths.length - 1])\n      })\n    },\n    //删除\n    deleteReport(row) {\n      let $this = this\n      this.$modal.confirm('是否确认删除？')\n        .then(function() {\n          businessReportDelete(row.id).then(res => {\n            $this.$message.success('删除成功')\n            $this.handleQuery()\n          })\n        }).catch(e => console.info(e))\n    },\n    //导出\n    exportReport() {\n      this.download('huida-reinsurance/reinsurance/businessReport/export', {\n        ...this.queryParams\n      }, `业务报表统计记录_${new Date().getTime()}.xlsx`)\n    }\n  }\n}\n</script>\n<style scoped>\n::v-deep .el-upload {\n  width: 100%;\n  padding-left: 20px;\n  padding-right: 20px;\n}\n::v-deep .el-upload-dragger {\n  width: 100%;\n}\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  padding: 20px 20px 0 0;\n}\n</style>\n"]}]}