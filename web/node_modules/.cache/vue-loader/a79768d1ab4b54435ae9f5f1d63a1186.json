{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\importDataConfig\\exportMgt.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\importDataConfig\\exportMgt.vue", "mtime": 1722240766000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750743148245}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750743149606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["exportMgt.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "exportMgt.vue", "sourceRoot": "src/views/reinsurance/importDataConfig", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\n      <el-form-item label=\"配置表\" prop=\"tableEn\" label-width=\"70px\">\n        <el-input v-model=\"queryParams.tableEn\" placeholder=\"请输入配置表\" clearable\n          @keyup.enter.native=\"handleQuery\" />\n      </el-form-item>\n      <el-form-item label=\"配置表名称\" prop=\"tableName\" label-width=\"90px\">\n        <el-input v-model=\"queryParams.tableName\" placeholder=\"配置表名称\" clearable\n          @keyup.enter.native=\"handleQuery\" />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\"\n          @click=\"handleExport\" v-hasPermi=\"['reinsurance:config:export']\">导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"configList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column v-for=\"column in configTableColumns\" :label=\"column.label\" :prop=\"column.prop\"\n        :key=\"column.prop\" :align=\"column.align\" :min-width=\"column.width\" v-if=\"column.visible\" show-overflow-tooltip>\n        <template slot-scope=\"{ row }\">\n          <template v-if=\"column.dict\">\n            <dict-tag :options=\"dict.type[column.dict]\" :value=\"row[column.prop]\"/>\n          </template>\n          <template v-else>{{ row[column.prop] }}</template>\n        </template>\n      </el-table-column>\n<!--      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" v-has-role=\"['admin']\">-->\n<!--        <template slot-scope=\"scope\">-->\n<!--          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\">修改</el-button>-->\n<!--          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>-->\n<!--        </template>-->\n<!--      </el-table-column>-->\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <el-dialog :title=\"dialog.title\" :visible.sync=\"dialog.visible\"\n      :close-on-click-modal=\"false\" :width=\"dialog.width\">\n      <component :is=\"dialog.componentsName\" v-if=\"dialog.visible\"\n        :params=\"dialog.params\" @ok=\"dialogOk\" @cancel=\"dialogCancel\" />\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport { listConfig } from \"@/api/reinsurance/importDataConfig\";\nimport ExportDataConfig from '@/views/reinsurance/importDataConfig/export.vue';\n\nexport default {\n  name: \"ExportDataConfigMgt\",\n  components: {\n    ExportDataConfig\n  },\n  dicts: [\n    'import_data_config_status',\n    'sys_user'\n  ],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 公共模态框\n      dialog: {\n        title: '',\n        visible: false,\n        width: '400px',\n        params: null,\n        componentsName: null,\n      },\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 导入导出数据配置表格数据\n      configList: [],\n      // 表格列配置\n      configTableColumns: [\n        { visible: true, align: 'center', width: '200px', key: 0, prop: 'tableEn', label: '配置表' },\n        { visible: true, align: 'center', width: '200px', key: 1, prop: 'tableName', label: '配置表名称' },\n      ],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        tableEn: null,\n        tableName: null,\n      },\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    //查询导入导出数据配置列表\n    getList() {\n      this.loading = true;\n      listConfig(this.queryParams).then(response => {\n        this.configList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 模态框确定\n    dialogOk() {\n      this.dialogCancel()\n      this.getList()\n    },\n    // 模态框取消\n    dialogCancel() {\n      this.dialog = {\n        title: '',\n        visible: false,\n        params: null,\n        componentsName: null,\n      }\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.dialog = {\n        title: '导出配置信息数据',\n        visible: true,\n        width: '450px',\n        params: {ids: this.ids, data: this.configList},\n        componentsName: 'ExportDataConfig',\n      }\n    }\n  }\n};\n</script>\n"]}]}