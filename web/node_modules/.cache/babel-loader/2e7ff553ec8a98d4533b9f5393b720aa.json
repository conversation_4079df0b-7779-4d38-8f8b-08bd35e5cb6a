{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\company\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\views\\reinsurance\\company\\index.vue", "mtime": 1722240766000}, {"path": "D:\\git\\huida-reinsurance\\web\\babel.config.js", "mtime": 1722236071000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750743148245}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750743149606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_company", "require", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "typeList", "companyCodeList", "title", "bankTitle", "open", "dataInfo", "companyCode", "list", "id", "tradeName", "accountCode", "tradeRegionCode", "tradeBankCode", "tradeBankName", "interbankCode", "updateBy", "createTime", "updateTime", "dataOpen", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "status", "form", "bankForm", "rules", "required", "message", "trigger", "companyAbbr", "companyName", "created", "getList", "getCompanyCodeList", "methods", "_this", "then", "response", "_this2", "companyList", "addDateRange", "rows", "cancel", "reset", "cbiCode", "financeCode", "ratingValue", "ratingOrg", "ratingDate", "overseas", "startDate", "endDate", "resetForm", "resetBank", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleSelectionChange", "selection", "map", "item", "length", "handleUpdate", "row", "_this3", "companyDetail", "submitForm", "_this4", "$refs", "validate", "valid", "undefined", "editCompany", "$modal", "msgSuccess", "addCompany", "handleBankInfoUnion", "_this5", "bankListByCompanyCode", "handleReturnBank", "handleUpdateBankInfo", "_this7", "confirm", "_this6", "editBank", "res", "$message", "type", "bankList", "catch", "addBank", "_this8", "$set", "handleDelete", "_this9", "delCompany", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/reinsurance/company/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\n\n      <el-form-item label=\"再保公司名称\" prop=\"companyCode\">\n\n        <el-select v-model=\"queryParams.companyCode\" placeholder=\"请输入再保公司名称\" clearable filterable>\n          <el-option v-for=\"item in companyCodeList\" :key=\"item.companyCode\"\n            :label=\"item.companyCode + ' ' + item.companyName\" :value=\"item.companyCode\" />\n        </el-select>\n      </el-form-item>\n\n      <el-form-item label=\"状态:\" prop=\"status\">\n        <el-select clearable v-model=\"queryParams.status\" placeholder=\"请选择状态\">\n          <el-option v-for=\"(item, index) in dict.type.cedeout_company_status\" :label=\"item.label\" :value=\"+item.value\"\n            :key=\"index + 'a'\" />\n        </el-select>\n      </el-form-item>\n\n\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">查询</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\"> 导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"typeList\" >\n      <!-- <el-table-column label=\"序号\" align=\"center\" prop=\"id\" :show-overflow-tooltip=\"true\" /> -->\n      <el-table-column label=\"再保公司编码\" align=\"center\" prop=\"companyCode\" :show-overflow-tooltip=\"true\" width=\"200\" />\n      <el-table-column label=\"再保公司简称\" align=\"center\" prop=\"companyAbbr\" :show-overflow-tooltip=\"true\" width=\"100\" />\n      <el-table-column label=\"再保公司全称\" align=\"center\" prop=\"companyName\" :show-overflow-tooltip=\"true\" width=\"100\" />\n      <el-table-column label=\"中保信编码\" align=\"center\" prop=\"cbiCode\" :show-overflow-tooltip=\"true\" width=\"150\" />\n      <el-table-column label=\"财务编码\" align=\"center\" prop=\"financeCode\" :show-overflow-tooltip=\"true\" width=\"100\" />\n      <el-table-column label=\"信用评级\" align=\"center\" prop=\"ratingValue\" :show-overflow-tooltip=\"true\" width=\"100\" />\n      <el-table-column label=\"评级机构\" align=\"center\" prop=\"ratingOrg\" :show-overflow-tooltip=\"true\" width=\"100\" />\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.cedeout_company_status\" :value=\"scope.row.status\" />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"评级时间\" align=\"center\" prop=\"ratingDate\" :show-overflow-tooltip=\"true\" width=\"100\" />\n      <el-table-column label=\"是否境外\" align=\"center\" prop=\"overseas\" :show-overflow-tooltip=\"true\">\n        <template slot-scope=\"scope\">\n          <dict-tag :options=\"dict.type.is_overseas\" :value=\"scope.row.overseas\" />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"起始时间\" align=\"center\" prop=\"startDate\" :show-overflow-tooltip=\"true\" width=\"100\" />\n      <el-table-column label=\"终止时间\" align=\"center\" prop=\"endDate\" :show-overflow-tooltip=\"true\" width=\"100\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding\" fixed=\"right\" width=\"220\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\">修改</el-button>\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-view\" @click=\"handleBankInfoUnion(scope.row)\">银行信息关联表</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\" />\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"150px\">\n        <el-row :gutter=\"10\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"再保公司编码:\" prop=\"companyCode\">\n              <el-input v-model=\"form.companyCode\" placeholder=\"请输入再保公司编码\" :disabled=\"form.id\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"再保公司简称:\" prop=\"companyAbbr\">\n              <el-input v-model=\"form.companyAbbr\" placeholder=\"请输入再保公司简称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"再保公司全称:\" prop=\"companyName\">\n              <el-input v-model=\"form.companyName\" placeholder=\"请输入再保公司全称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"中保信编码:\" prop=\"cbiCode\">\n              <el-input v-model=\"form.cbiCode\" placeholder=\"请输入中保信编码\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"财务编码:\" prop=\"financeCode\">\n              <el-input v-model=\"form.financeCode\" placeholder=\"请输入财务编码\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"信用评级:\" prop=\"ratingValue\">\n              <el-input v-model=\"form.ratingValue\" placeholder=\"请输入信用评级\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评级机构:\" prop=\"ratingOrg\">\n              <el-input v-model=\"form.ratingOrg\" placeholder=\"请输入评级机构\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"评级时间:\" prop=\"ratingDate\">\n              <el-date-picker v-model=\"form.ratingDate\" type=\"date\" format=\"yyyy-MM-dd\" placeholder=\"请输入评级时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否境外:\" prop=\"overseas\">\n              <el-select v-model=\"form.overseas\" placeholder=\"请选择\" style=\"width: 225px;\">\n                <el-option v-for=\"(item, index) in dict.type.is_overseas\" :label=\"item.label\" :value=\"+item.value\"\n                  :key=\"index + 'a'\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"起始时间:\" prop=\"startDate\">\n              <el-date-picker v-model=\"form.startDate\" type=\"date\" format=\"yyyy-MM-dd\" placeholder=\"请输入起始时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"终止时间:\" prop=\"endDate\">\n              <el-date-picker v-model=\"form.endDate\" type=\"date\" format=\"yyyy-MM-dd\" placeholder=\"请输入终止时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n\n    <!-- <el-dialog :title=\"bankTitle\" :visible.sync=\"dataOpen\" width=\"1200px\" >\n      <el-table v-loading=\"loading\" :data=\"dataInfo\" @selection-change=\"handleSelectionChange\">\n        <el-table-column label=\"交易方名称\" align=\"center\" prop=\"tradeName\" width=\"100\" >\n        </el-table-column>\n        <el-table-column label=\"账户编码\" align=\"center\" prop=\"accountCode\" width=\"100\" />\n        <el-table-column label=\"交易方区域编码\" align=\"center\" prop=\"tradeRegionCode\" width=\"120\" />\n        <el-table-column label=\"交易方银行编码\" align=\"center\" prop=\"tradeBankCode\" width=\"120\" />\n        <el-table-column label=\"交易方开户银行名称\" align=\"center\" prop=\"tradeBankName\" width=\"150\" />\n        <el-table-column label=\"联行号编码\" align=\"center\" prop=\"interbankCode\" width=\"100\" />\n        <el-table-column label=\"操作人\" align=\"center\" prop=\"updateBy\" width=\"100\" />\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"100\" />\n        <el-table-column label=\"修改时间\" align=\"center\" prop=\"updateTime\" width=\"100\" />\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding\" fixed=\"right\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdateBankInfo(scope.row)\">修改</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-dialog> -->\n\n    <el-dialog :title=\"bankTitle\" :visible.sync=\"dataOpen\" width=\"1200px\">\n      <el-form ref=\"bankForm\" :model=\"bankForm\">\n        <el-form-item>\n          <el-table v-loading=\"loading\" :data=\"dataInfo.list\" @selection-change=\"handleSelectionChange\">\n            <el-table-column label=\"交易方名称\" align=\"center\" prop=\"tradeName\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <el-input type=\"input\" autosize resize=\"none\" v-model=\"scope.row.tradeName\">\n                </el-input>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"账户编码\" align=\"center\" prop=\"accountCode\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <el-input type=\"input\" autosize resize=\"none\" v-model=\"scope.row.accountCode\">\n                </el-input>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"交易方区域编码\" align=\"center\" prop=\"tradeRegionCode\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <el-input type=\"input\" autosize resize=\"none\" v-model=\"scope.row.tradeRegionCode\">\n                </el-input>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"交易方银行编码\" align=\"center\" prop=\"tradeBankCode\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <el-input type=\"input\" autosize resize=\"none\" v-model=\"scope.row.tradeBankCode\">\n                </el-input>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"交易方开户银行名称\" align=\"center\" prop=\"tradeBankName\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <el-input type=\"input\" autosize resize=\"none\" v-model=\"scope.row.tradeBankName\">\n                </el-input>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"联行号编码\" align=\"center\" prop=\"interbankCode\" width=\"250\">\n              <template slot-scope=\"scope\">\n                <el-input type=\"input\" autosize resize=\"none\" v-model=\"scope.row.interbankCode\">\n                </el-input>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作人\" align=\"center\" prop=\"updateBy\" width=\"100\">\n              <template slot-scope=\"scope\">\n                <el-input type=\"input\" autosize resize=\"none\" v-model=\"scope.row.updateBy\" disabled>\n                </el-input>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <el-input type=\"input\" autosize resize=\"none\" v-model=\"scope.row.createTime\" disabled>\n                </el-input>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"修改时间\" align=\"center\" prop=\"updateTime\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <el-input type=\"input\" autosize resize=\"none\" v-model=\"scope.row.updateTime\" disabled>\n                </el-input>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding\" fixed=\"right\" width=\"100\">\n              <template slot-scope=\"scope\">\n\n                <el-button type=\"text\" size=\"mini\" @click=\"handleUpdateBankInfo(scope.row)\"\n                  v-if=\"scope.row.id\">修改</el-button>\n                <el-button type=\"text\" size=\"mini\" @click=\"handleUpdateBankInfo(scope.row)\" v-else>保存</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-form-item>\n      </el-form>\n    </el-dialog>\n\n\n  </div>\n</template>\n\n<script>\nimport { companyList, companyDetail, delCompany, addCompany, editCompany, getCompanyCodeList, bankList, getBankInfo, getBankInfoByCompanyCode, addBank, editBank, bankListByCompanyCode } from \"../../../api/reinsurance/company\";\n\nexport default {\n  name: \"Company\",\n  dicts: ['is_overseas', 'cedeout_company_status'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 字典表格数据\n      typeList: [],\n      companyCodeList: [],\n      // 弹出层标题\n      title: \"\",\n      bankTitle: \"\",\n      // 是否显示弹出层\n      open: false,\n      // dataInfo: [{\n      //   id: null,\n      //   tradeName: null,\n      //   accountCode: null,\n      //   tradeRegionCode: null,\n      //   tradeBankCode: null,\n      //   tradeBankName: null,\n      //   interbankCode: null,\n      //   updateBy: null,\n      //   createTime: null,\n      //   updateTime: null,\n      // }],\n      dataInfo: {\n        companyCode: null,\n        list: [{\n          id: null,\n          tradeName: null,\n          accountCode: null,\n          tradeRegionCode: null,\n          tradeBankCode: null,\n          tradeBankName: null,\n          interbankCode: null,\n          updateBy: null,\n          createTime: null,\n          updateTime: null,\n        }]\n      },\n      dataOpen: false,\n      //bankOpen: false,\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        companyCode: null,\n        status: 0,\n      },\n      // 表单参数\n      form: {},\n      bankForm: {},\n      // 表单校验\n      rules: {\n        companyCode: [\n          { required: true, message: \"再保公司编码不能为空\", trigger: \"blur\" }\n        ],\n        companyAbbr: [\n          { required: true, message: \"再保公司简称不能为空\", trigger: \"blur\" }\n        ],\n        companyName: [\n          { required: true, message: \"再保公司全称不能为空\", trigger: \"change\" }\n        ],\n      }\n    };\n  },\n  created() {\n    this.getList();\n    this.getCompanyCodeList();\n  },\n  methods: {\n\n    getCompanyCodeList() {\n      getCompanyCodeList().then(response => {\n        this.companyCodeList = response.data;\n      }\n      );\n    },\n\n    /** 查询字典类型列表 */\n    getList() {\n      this.loading = true;\n      companyList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\n        this.typeList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      }\n      );\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        companyCode: null,\n        companyAbbr: null,\n        companyName: null,\n        cbiCode: null,\n        financeCode: null,\n        ratingValue: null,\n        ratingOrg: null,\n        ratingDate: null,\n        overseas: null,\n        startDate: null,\n        endDate: null,\n        status: 0,\n      };\n      this.resetForm(\"form\");\n    },\n\n    // 表单重置\n    resetBank() {\n      this.bankForm = {\n        id: null,\n        companyCode: null,\n        tradeName: null,\n        accountCode: null,\n        tradeRegionCode: null,\n        tradeBankCode: null,\n        tradeBankName: null,\n        interbankCode: null,\n        updateBy: null,\n        createTime: null,\n        updateTime: null,\n      };\n      this.resetForm(\"bankForm\");\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加再保公司\";\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length != 1\n      this.multiple = !selection.length\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      companyDetail(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改再保公司信息\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != undefined) {\n            editCompany(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addCompany(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n\n    /** 详情 */\n    handleBankInfoUnion(row) {\n      this.resetBank();\n      this.loading = true;\n      const companyCode = row.companyCode\n      bankListByCompanyCode(companyCode).then(response => {\n        this.dataInfo.companyCode = companyCode;\n\n        this.dataInfo.list = response.data.length > 0 ? response.data : [{\n          id: null,\n          tradeName: null,\n          accountCode: null,\n          tradeRegionCode: null,\n          tradeBankCode: null,\n          tradeBankName: null,\n          interbankCode: null,\n          updateBy: null,\n          createTime: null,\n          updateTime: null,\n        }]\n\n        this.loading = false;\n        this.bankTitle = \"查看银行详情\";\n        this.dataOpen = true;\n      }\n      );\n    },\n    /** 取消银行信息 */\n    handleReturnBank() {\n      this.dataOpen = false;\n    },\n\n    handleUpdateBankInfo(data) {\n      data.companyCode = this.dataInfo.companyCode;\n      if (data.id) {\n        this.$modal.confirm('是否确认修改交易方名称为\"' + data.tradeName + '\"的数据项？').then(function () {\n          return editBank(data).then((res) => {\n            this.$message({\n              message: \"修改成功\",\n              type: \"success\",\n            });\n            this.bankList(data);\n          });\n        }).then(() => {\n          this.bankList(data);\n          this.$modal.msgSuccess(\"修改成功\");\n        }).catch(() => { });\n      } else {\n        addBank(data).then(res => {\n          this.$message({\n            message: \"新增成功\",\n            type: \"success\",\n          });\n          this.bankList(data);\n        });\n      }\n    },\n    bankList(data) {\n      bankList(data).then(response => {\n        this.dataInfo.companyCode = data.companyCode;\n        this.$set(this.dataInfo, 'list', response.rows.length > 0 ? response.rows : [{\n          id: null,\n          tradeName: null,\n          accountCode: null,\n          tradeRegionCode: null,\n          tradeBankCode: null,\n          tradeBankName: null,\n          interbankCode: null,\n          updateBy: null,\n          createTime: null,\n          updateTime: null,\n        }])\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      const companyCode = row.companyCode\n      this.$modal.confirm('是否确认删除再保公司编码为\"' + row.companyCode + '\"的数据项？').then(function () {\n        return delCompany(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => { });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('huida-reinsurance/reinsurance/company/export', {\n        ...this.queryParams\n      }, `type_${new Date().getTime()}.xlsx`)\n    },\n  }\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;AAgPA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACAC,eAAA;MACA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,IAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC,QAAA;QACAC,WAAA;QACAC,IAAA;UACAC,EAAA;UACAC,SAAA;UACAC,WAAA;UACAC,eAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAC,QAAA;UACAC,UAAA;UACAC,UAAA;QACA;MACA;MACAC,QAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAhB,WAAA;QACAiB,MAAA;MACA;MACA;MACAC,IAAA;MACAC,QAAA;MACA;MACAC,KAAA;QACApB,WAAA,GACA;UAAAqB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,WAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,WAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA;IAEAD,kBAAA,WAAAA,mBAAA;MAAA,IAAAE,KAAA;MACA,IAAAF,2BAAA,IAAAG,IAAA,WAAAC,QAAA;QACAF,KAAA,CAAAnC,eAAA,GAAAqC,QAAA,CAAA7C,IAAA;MACA,CACA;IACA;IAEA,eACAwC,OAAA,WAAAA,QAAA;MAAA,IAAAM,MAAA;MACA,KAAA7C,OAAA;MACA,IAAA8C,oBAAA,OAAAC,YAAA,MAAArB,WAAA,OAAAD,SAAA,GAAAkB,IAAA,WAAAC,QAAA;QACAC,MAAA,CAAAvC,QAAA,GAAAsC,QAAA,CAAAI,IAAA;QACAH,MAAA,CAAAxC,KAAA,GAAAuC,QAAA,CAAAvC,KAAA;QACAwC,MAAA,CAAA7C,OAAA;MACA,CACA;IACA;IACA;IACAiD,MAAA,WAAAA,OAAA;MACA,KAAAvC,IAAA;MACA,KAAAwC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAApB,IAAA;QACAhB,EAAA;QACAF,WAAA;QACAwB,WAAA;QACAC,WAAA;QACAc,OAAA;QACAC,WAAA;QACAC,WAAA;QACAC,SAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,OAAA;QACA7B,MAAA;MACA;MACA,KAAA8B,SAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAA7B,QAAA;QACAjB,EAAA;QACAF,WAAA;QACAG,SAAA;QACAC,WAAA;QACAC,eAAA;QACAC,aAAA;QACAC,aAAA;QACAC,aAAA;QACAC,QAAA;QACAC,UAAA;QACAC,UAAA;MACA;MACA,KAAAoC,SAAA;IACA;IAEA,aACAE,WAAA,WAAAA,YAAA;MACA,KAAAnC,WAAA,CAAAC,OAAA;MACA,KAAAY,OAAA;IACA;IACA,aACAuB,UAAA,WAAAA,WAAA;MACA,KAAArC,SAAA;MACA,KAAAkC,SAAA;MACA,KAAAE,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAAxC,IAAA;MACA,KAAAF,KAAA;IACA;IACA;IACAwD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAhE,GAAA,GAAAgE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAArD,EAAA;MAAA;MACA,KAAAZ,MAAA,GAAA+D,SAAA,CAAAG,MAAA;MACA,KAAAjE,QAAA,IAAA8D,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAArB,KAAA;MACA,IAAApC,EAAA,GAAAwD,GAAA,CAAAxD,EAAA,SAAAb,GAAA;MACA,IAAAuE,sBAAA,EAAA1D,EAAA,EAAA6B,IAAA,WAAAC,QAAA;QACA2B,MAAA,CAAAzC,IAAA,GAAAc,QAAA,CAAA7C,IAAA;QACAwE,MAAA,CAAA7D,IAAA;QACA6D,MAAA,CAAA/D,KAAA;MACA;IACA;IACA;IACAiE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA5C,IAAA,CAAAhB,EAAA,IAAAgE,SAAA;YACA,IAAAC,oBAAA,EAAAL,MAAA,CAAA5C,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACA8B,MAAA,CAAAM,MAAA,CAAAC,UAAA;cACAP,MAAA,CAAAhE,IAAA;cACAgE,MAAA,CAAAnC,OAAA;YACA;UACA;YACA,IAAA2C,mBAAA,EAAAR,MAAA,CAAA5C,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACA8B,MAAA,CAAAM,MAAA,CAAAC,UAAA;cACAP,MAAA,CAAAhE,IAAA;cACAgE,MAAA,CAAAnC,OAAA;YACA;UACA;QACA;MACA;IACA;IAEA,SACA4C,mBAAA,WAAAA,oBAAAb,GAAA;MAAA,IAAAc,MAAA;MACA,KAAAxB,SAAA;MACA,KAAA5D,OAAA;MACA,IAAAY,WAAA,GAAA0D,GAAA,CAAA1D,WAAA;MACA,IAAAyE,8BAAA,EAAAzE,WAAA,EAAA+B,IAAA,WAAAC,QAAA;QACAwC,MAAA,CAAAzE,QAAA,CAAAC,WAAA,GAAAA,WAAA;QAEAwE,MAAA,CAAAzE,QAAA,CAAAE,IAAA,GAAA+B,QAAA,CAAA7C,IAAA,CAAAqE,MAAA,OAAAxB,QAAA,CAAA7C,IAAA;UACAe,EAAA;UACAC,SAAA;UACAC,WAAA;UACAC,eAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAC,QAAA;UACAC,UAAA;UACAC,UAAA;QACA;QAEA6D,MAAA,CAAApF,OAAA;QACAoF,MAAA,CAAA3E,SAAA;QACA2E,MAAA,CAAA5D,QAAA;MACA,CACA;IACA;IACA,aACA8D,gBAAA,WAAAA,iBAAA;MACA,KAAA9D,QAAA;IACA;IAEA+D,oBAAA,WAAAA,qBAAAxF,IAAA;MAAA,IAAAyF,MAAA;MACAzF,IAAA,CAAAa,WAAA,QAAAD,QAAA,CAAAC,WAAA;MACA,IAAAb,IAAA,CAAAe,EAAA;QACA,KAAAkE,MAAA,CAAAS,OAAA,mBAAA1F,IAAA,CAAAgB,SAAA,aAAA4B,IAAA;UAAA,IAAA+C,MAAA;UACA,WAAAC,iBAAA,EAAA5F,IAAA,EAAA4C,IAAA,WAAAiD,GAAA;YACAF,MAAA,CAAAG,QAAA;cACA3D,OAAA;cACA4D,IAAA;YACA;YACAJ,MAAA,CAAAK,QAAA,CAAAhG,IAAA;UACA;QACA,GAAA4C,IAAA;UACA6C,MAAA,CAAAO,QAAA,CAAAhG,IAAA;UACAyF,MAAA,CAAAR,MAAA,CAAAC,UAAA;QACA,GAAAe,KAAA;MACA;QACA,IAAAC,gBAAA,EAAAlG,IAAA,EAAA4C,IAAA,WAAAiD,GAAA;UACAJ,MAAA,CAAAK,QAAA;YACA3D,OAAA;YACA4D,IAAA;UACA;UACAN,MAAA,CAAAO,QAAA,CAAAhG,IAAA;QACA;MACA;IACA;IACAgG,QAAA,WAAAA,SAAAhG,IAAA;MAAA,IAAAmG,MAAA;MACA,IAAAH,iBAAA,EAAAhG,IAAA,EAAA4C,IAAA,WAAAC,QAAA;QACAsD,MAAA,CAAAvF,QAAA,CAAAC,WAAA,GAAAb,IAAA,CAAAa,WAAA;QACAsF,MAAA,CAAAC,IAAA,CAAAD,MAAA,CAAAvF,QAAA,UAAAiC,QAAA,CAAAI,IAAA,CAAAoB,MAAA,OAAAxB,QAAA,CAAAI,IAAA;UACAlC,EAAA;UACAC,SAAA;UACAC,WAAA;UACAC,eAAA;UACAC,aAAA;UACAC,aAAA;UACAC,aAAA;UACAC,QAAA;UACAC,UAAA;UACAC,UAAA;QACA;MACA;IACA;IACA,aACA6E,YAAA,WAAAA,aAAA9B,GAAA;MAAA,IAAA+B,MAAA;MACA,IAAApG,GAAA,GAAAqE,GAAA,CAAAxD,EAAA,SAAAb,GAAA;MACA,IAAAW,WAAA,GAAA0D,GAAA,CAAA1D,WAAA;MACA,KAAAoE,MAAA,CAAAS,OAAA,oBAAAnB,GAAA,CAAA1D,WAAA,aAAA+B,IAAA;QACA,WAAA2D,mBAAA,EAAArG,GAAA;MACA,GAAA0C,IAAA;QACA0D,MAAA,CAAA9D,OAAA;QACA8D,MAAA,CAAArB,MAAA,CAAAC,UAAA;MACA,GAAAe,KAAA;IACA;IACA,aACAO,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,qDAAAC,cAAA,CAAAC,OAAA,MACA,KAAAhF,WAAA,WAAAiF,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}