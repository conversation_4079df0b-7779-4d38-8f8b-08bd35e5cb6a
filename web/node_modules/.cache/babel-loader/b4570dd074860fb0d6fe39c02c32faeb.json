{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\git\\huida-reinsurance\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\git\\huida-reinsurance\\web\\src\\plugins\\auth.js", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\plugins\\auth.js", "mtime": 1722236072000}, {"path": "D:\\git\\huida-reinsurance\\web\\babel.config.js", "mtime": 1722236071000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750743148245}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750743145959}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9naXQvaHVpZGEtcmVpbnN1cmFuY2Uvd2ViL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmV2ZXJ5LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5pdGVyYXRvci5zb21lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnZhciBfc3RvcmUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvc3RvcmUiKSk7CmZ1bmN0aW9uIGF1dGhQZXJtaXNzaW9uKHBlcm1pc3Npb24pIHsKICB2YXIgYWxsX3Blcm1pc3Npb24gPSAiKjoqOioiOwogIHZhciBwZXJtaXNzaW9ucyA9IF9zdG9yZS5kZWZhdWx0LmdldHRlcnMgJiYgX3N0b3JlLmRlZmF1bHQuZ2V0dGVycy5wZXJtaXNzaW9uczsKICBpZiAocGVybWlzc2lvbiAmJiBwZXJtaXNzaW9uLmxlbmd0aCA+IDApIHsKICAgIHJldHVybiBwZXJtaXNzaW9ucy5zb21lKGZ1bmN0aW9uICh2KSB7CiAgICAgIHJldHVybiBhbGxfcGVybWlzc2lvbiA9PT0gdiB8fCB2ID09PSBwZXJtaXNzaW9uOwogICAgfSk7CiAgfSBlbHNlIHsKICAgIHJldHVybiBmYWxzZTsKICB9Cn0KZnVuY3Rpb24gYXV0aFJvbGUocm9sZSkgewogIHZhciBzdXBlcl9hZG1pbiA9ICJhZG1pbiI7CiAgdmFyIHJvbGVzID0gX3N0b3JlLmRlZmF1bHQuZ2V0dGVycyAmJiBfc3RvcmUuZGVmYXVsdC5nZXR0ZXJzLnJvbGVzOwogIGlmIChyb2xlICYmIHJvbGUubGVuZ3RoID4gMCkgewogICAgcmV0dXJuIHJvbGVzLnNvbWUoZnVuY3Rpb24gKHYpIHsKICAgICAgcmV0dXJuIHN1cGVyX2FkbWluID09PSB2IHx8IHYgPT09IHJvbGU7CiAgICB9KTsKICB9IGVsc2UgewogICAgcmV0dXJuIGZhbHNlOwogIH0KfQp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgLy8g6aqM6K+B55So5oi35piv5ZCm5YW35aSH5p+Q5p2D6ZmQCiAgaGFzUGVybWk6IGZ1bmN0aW9uIGhhc1Blcm1pKHBlcm1pc3Npb24pIHsKICAgIHJldHVybiBhdXRoUGVybWlzc2lvbihwZXJtaXNzaW9uKTsKICB9LAogIC8vIOmqjOivgeeUqOaIt+aYr+WQpuWQq+acieaMh+Wumuadg+mZkO+8jOWPqumcgOWMheWQq+WFtuS4reS4gOS4qgogIGhhc1Blcm1pT3I6IGZ1bmN0aW9uIGhhc1Blcm1pT3IocGVybWlzc2lvbnMpIHsKICAgIHJldHVybiBwZXJtaXNzaW9ucy5zb21lKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgIHJldHVybiBhdXRoUGVybWlzc2lvbihpdGVtKTsKICAgIH0pOwogIH0sCiAgLy8g6aqM6K+B55So5oi35piv5ZCm5ZCr5pyJ5oyH5a6a5p2D6ZmQ77yM5b+F6aG75YWo6YOo5oul5pyJCiAgaGFzUGVybWlBbmQ6IGZ1bmN0aW9uIGhhc1Blcm1pQW5kKHBlcm1pc3Npb25zKSB7CiAgICByZXR1cm4gcGVybWlzc2lvbnMuZXZlcnkoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgcmV0dXJuIGF1dGhQZXJtaXNzaW9uKGl0ZW0pOwogICAgfSk7CiAgfSwKICAvLyDpqozor4HnlKjmiLfmmK/lkKblhbflpIfmn5Dop5LoibIKICBoYXNSb2xlOiBmdW5jdGlvbiBoYXNSb2xlKHJvbGUpIHsKICAgIHJldHVybiBhdXRoUm9sZShyb2xlKTsKICB9LAogIC8vIOmqjOivgeeUqOaIt+aYr+WQpuWQq+acieaMh+WumuinkuiJsu+8jOWPqumcgOWMheWQq+WFtuS4reS4gOS4qgogIGhhc1JvbGVPcjogZnVuY3Rpb24gaGFzUm9sZU9yKHJvbGVzKSB7CiAgICByZXR1cm4gcm9sZXMuc29tZShmdW5jdGlvbiAoaXRlbSkgewogICAgICByZXR1cm4gYXV0aFJvbGUoaXRlbSk7CiAgICB9KTsKICB9LAogIC8vIOmqjOivgeeUqOaIt+aYr+WQpuWQq+acieaMh+WumuinkuiJsu+8jOW/hemhu+WFqOmDqOaLpeaciQogIGhhc1JvbGVBbmQ6IGZ1bmN0aW9uIGhhc1JvbGVBbmQocm9sZXMpIHsKICAgIHJldHVybiByb2xlcy5ldmVyeShmdW5jdGlvbiAoaXRlbSkgewogICAgICByZXR1cm4gYXV0aFJvbGUoaXRlbSk7CiAgICB9KTsKICB9Cn07"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "authPermission", "permission", "all_permission", "permissions", "store", "getters", "length", "some", "v", "authRole", "role", "super_admin", "roles", "_default", "exports", "default", "<PERSON><PERSON><PERSON><PERSON>", "hasPermiOr", "item", "hasPermiAnd", "every", "hasRole", "hasRoleOr", "hasRoleAnd"], "sources": ["D:/git/huida-reinsurance/web/src/plugins/auth.js"], "sourcesContent": ["import store from '@/store'\n\nfunction authPermission(permission) {\n  const all_permission = \"*:*:*\";\n  const permissions = store.getters && store.getters.permissions\n  if (permission && permission.length > 0) {\n    return permissions.some(v => {\n      return all_permission === v || v === permission\n    })\n  } else {\n    return false\n  }\n}\n\nfunction authRole(role) {\n  const super_admin = \"admin\";\n  const roles = store.getters && store.getters.roles\n  if (role && role.length > 0) {\n    return roles.some(v => {\n      return super_admin === v || v === role\n    })\n  } else {\n    return false\n  }\n}\n\nexport default {\n  // 验证用户是否具备某权限\n  hasPermi(permission) {\n    return authPermission(permission);\n  },\n  // 验证用户是否含有指定权限，只需包含其中一个\n  hasPermiOr(permissions) {\n    return permissions.some(item => {\n      return authPermission(item)\n    })\n  },\n  // 验证用户是否含有指定权限，必须全部拥有\n  hasPermiAnd(permissions) {\n    return permissions.every(item => {\n      return authPermission(item)\n    })\n  },\n  // 验证用户是否具备某角色\n  hasRole(role) {\n    return authRole(role);\n  },\n  // 验证用户是否含有指定角色，只需包含其中一个\n  hasRoleOr(roles) {\n    return roles.some(item => {\n      return authRole(item)\n    })\n  },\n  // 验证用户是否含有指定角色，必须全部拥有\n  hasRoleAnd(roles) {\n    return roles.every(item => {\n      return authRole(item)\n    })\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,SAASC,cAAcA,CAACC,UAAU,EAAE;EAClC,IAAMC,cAAc,GAAG,OAAO;EAC9B,IAAMC,WAAW,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,WAAW;EAC9D,IAAIF,UAAU,IAAIA,UAAU,CAACK,MAAM,GAAG,CAAC,EAAE;IACvC,OAAOH,WAAW,CAACI,IAAI,CAAC,UAAAC,CAAC,EAAI;MAC3B,OAAON,cAAc,KAAKM,CAAC,IAAIA,CAAC,KAAKP,UAAU;IACjD,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAEA,SAASQ,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAMC,WAAW,GAAG,OAAO;EAC3B,IAAMC,KAAK,GAAGR,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACO,KAAK;EAClD,IAAIF,IAAI,IAAIA,IAAI,CAACJ,MAAM,GAAG,CAAC,EAAE;IAC3B,OAAOM,KAAK,CAACL,IAAI,CAAC,UAAAC,CAAC,EAAI;MACrB,OAAOG,WAAW,KAAKH,CAAC,IAAIA,CAAC,KAAKE,IAAI;IACxC,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc;EACb;EACAC,QAAQ,WAARA,QAAQA,CAACf,UAAU,EAAE;IACnB,OAAOD,cAAc,CAACC,UAAU,CAAC;EACnC,CAAC;EACD;EACAgB,UAAU,WAAVA,UAAUA,CAACd,WAAW,EAAE;IACtB,OAAOA,WAAW,CAACI,IAAI,CAAC,UAAAW,IAAI,EAAI;MAC9B,OAAOlB,cAAc,CAACkB,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD;EACAC,WAAW,WAAXA,WAAWA,CAAChB,WAAW,EAAE;IACvB,OAAOA,WAAW,CAACiB,KAAK,CAAC,UAAAF,IAAI,EAAI;MAC/B,OAAOlB,cAAc,CAACkB,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD;EACAG,OAAO,WAAPA,OAAOA,CAACX,IAAI,EAAE;IACZ,OAAOD,QAAQ,CAACC,IAAI,CAAC;EACvB,CAAC;EACD;EACAY,SAAS,WAATA,SAASA,CAACV,KAAK,EAAE;IACf,OAAOA,KAAK,CAACL,IAAI,CAAC,UAAAW,IAAI,EAAI;MACxB,OAAOT,QAAQ,CAACS,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EACD;EACAK,UAAU,WAAVA,UAAUA,CAACX,KAAK,EAAE;IAChB,OAAOA,KAAK,CAACQ,KAAK,CAAC,UAAAF,IAAI,EAAI;MACzB,OAAOT,QAAQ,CAACS,IAAI,CAAC;IACvB,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}]}