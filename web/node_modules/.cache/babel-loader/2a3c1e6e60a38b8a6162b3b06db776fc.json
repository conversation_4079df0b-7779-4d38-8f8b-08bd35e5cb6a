{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\git\\huida-reinsurance\\web\\src\\components\\IconSelect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\components\\IconSelect\\index.vue", "mtime": 1722236072000}, {"path": "D:\\git\\huida-reinsurance\\web\\babel.config.js", "mtime": 1722236071000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750743148245}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750743149606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9naXQvaHVpZGEtcmVpbnN1cmFuY2Uvd2ViL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLml0ZXJhdG9yLmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnZhciBfcmVxdWlyZUljb25zID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL3JlcXVpcmVJY29ucyIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICdJY29uU2VsZWN0JywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbmFtZTogJycsCiAgICAgIGljb25MaXN0OiBfcmVxdWlyZUljb25zLmRlZmF1bHQKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBmaWx0ZXJJY29uczogZnVuY3Rpb24gZmlsdGVySWNvbnMoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuaWNvbkxpc3QgPSBfcmVxdWlyZUljb25zLmRlZmF1bHQ7CiAgICAgIGlmICh0aGlzLm5hbWUpIHsKICAgICAgICB0aGlzLmljb25MaXN0ID0gdGhpcy5pY29uTGlzdC5maWx0ZXIoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIHJldHVybiBpdGVtLmluY2x1ZGVzKF90aGlzLm5hbWUpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgc2VsZWN0ZWRJY29uOiBmdW5jdGlvbiBzZWxlY3RlZEljb24obmFtZSkgewogICAgICB0aGlzLiRlbWl0KCdzZWxlY3RlZCcsIG5hbWUpOwogICAgICBkb2N1bWVudC5ib2R5LmNsaWNrKCk7CiAgICB9LAogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLm5hbWUgPSAnJzsKICAgICAgdGhpcy5pY29uTGlzdCA9IF9yZXF1aXJlSWNvbnMuZGVmYXVsdDsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_requireIcons", "_interopRequireDefault", "require", "name", "data", "iconList", "icons", "methods", "filterIcons", "_this", "filter", "item", "includes", "selectedIcon", "$emit", "document", "body", "click", "reset"], "sources": ["src/components/IconSelect/index.vue"], "sourcesContent": ["<!-- <AUTHOR> -->\n<template>\n  <div class=\"icon-body\">\n    <el-input v-model=\"name\" style=\"position: relative;\" clearable placeholder=\"请输入图标名称\" @clear=\"filterIcons\" @input.native=\"filterIcons\">\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\" />\n    </el-input>\n    <div class=\"icon-list\">\n      <div v-for=\"(item, index) in iconList\" :key=\"index\" @click=\"selectedIcon(item)\">\n        <svg-icon :icon-class=\"item\" style=\"height: 30px;width: 16px;\" />\n        <span>{{ item }}</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport icons from './requireIcons'\nexport default {\n  name: 'IconSelect',\n  data() {\n    return {\n      name: '',\n      iconList: icons\n    }\n  },\n  methods: {\n    filterIcons() {\n      this.iconList = icons\n      if (this.name) {\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\n      }\n    },\n    selectedIcon(name) {\n      this.$emit('selected', name)\n      document.body.click()\n    },\n    reset() {\n      this.name = ''\n      this.iconList = icons\n    }\n  }\n}\n</script>\n\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\n  .icon-body {\n    width: 100%;\n    padding: 10px;\n    .icon-list {\n      height: 200px;\n      overflow-y: scroll;\n      div {\n        height: 30px;\n        line-height: 30px;\n        margin-bottom: -5px;\n        cursor: pointer;\n        width: 33%;\n        float: left;\n      }\n      span {\n        display: inline-block;\n        vertical-align: -0.15em;\n        fill: currentColor;\n        overflow: hidden;\n      }\n    }\n  }\n</style>\n"], "mappings": ";;;;;;;;;;;;;;AAgBA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAD,IAAA;MACAE,QAAA,EAAAC;IACA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,KAAA;MACA,KAAAJ,QAAA,GAAAC,qBAAA;MACA,SAAAH,IAAA;QACA,KAAAE,QAAA,QAAAA,QAAA,CAAAK,MAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,QAAA,CAAAH,KAAA,CAAAN,IAAA;QAAA;MACA;IACA;IACAU,YAAA,WAAAA,aAAAV,IAAA;MACA,KAAAW,KAAA,aAAAX,IAAA;MACAY,QAAA,CAAAC,IAAA,CAAAC,KAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAf,IAAA;MACA,KAAAE,QAAA,GAAAC,qBAAA;IACA;EACA;AACA", "ignoreList": []}]}