{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\git\\huida-reinsurance\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\git\\huida-reinsurance\\web\\src\\utils\\ruoyi.js", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\utils\\ruoyi.js", "mtime": 1722236072000}, {"path": "D:\\git\\huida-reinsurance\\web\\babel.config.js", "mtime": 1722236071000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750743148245}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750743145959}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "parseTime", "time", "pattern", "arguments", "length", "format", "date", "_typeof2", "default", "test", "parseInt", "replace", "RegExp", "toString", "Date", "formatObj", "y", "getFullYear", "m", "getMonth", "d", "getDate", "h", "getHours", "i", "getMinutes", "s", "getSeconds", "a", "getDay", "time_str", "result", "key", "value", "resetForm", "refName", "$refs", "resetFields", "addDateRange", "params", "date<PERSON><PERSON><PERSON>", "propNameS", "propNameE", "search", "Array", "isArray", "selectDictLabel", "datas", "undefined", "actions", "Object", "keys", "some", "push", "label", "join", "selectDictLabels", "separator", "currentSeparator", "temp", "split", "val", "match", "substring", "sprintf", "str", "args", "flag", "arg", "parseStrEmpty", "mergeRecursive", "source", "target", "p", "constructor", "e", "handleTree", "data", "id", "parentId", "children", "config", "childrenList", "childrenListMap", "nodeIds", "tree", "_iterator", "_createForOfIteratorHelper2", "_step", "n", "done", "err", "f", "_iterator2", "_step2", "_i", "_tree", "t", "adaptToChildrenList", "o", "_iterator3", "_step3", "c", "tansParams", "_i2", "_Object$keys", "propName", "part", "encodeURIComponent", "_i3", "_Object$keys2", "subPart", "blobValidate", "_x", "_blobValidate", "apply", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "text", "wrap", "_callee$", "_context", "prev", "next", "sent", "JSON", "parse", "abrupt", "t0", "stop", "columnHasPermi", "all_permission", "permissions", "store", "getters", "permissionFlag", "hasPermissions", "permission", "includes", "Error"], "sources": ["D:/git/huida-reinsurance/web/src/utils/ruoyi.js"], "sourcesContent": ["import store from \"@/store\";\n\n\n/**\n * 通用js方法封装处理\n * Copyright (c) 2019 ruoyi\n */\n\n// 日期格式化\nexport function parseTime(time, pattern) {\n  if (arguments.length === 0 || !time) {\n    return null\n  }\n  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'\n  let date\n  if (typeof time === 'object') {\n    date = time\n  } else {\n    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {\n      time = parseInt(time)\n    } else if (typeof time === 'string') {\n      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\\.[\\d]{3}/gm), '');\n    }\n    if ((typeof time === 'number') && (time.toString().length === 10)) {\n      time = time * 1000\n    }\n    date = new Date(time)\n  }\n  const formatObj = {\n    y: date.getFullYear(),\n    m: date.getMonth() + 1,\n    d: date.getDate(),\n    h: date.getHours(),\n    i: date.getMinutes(),\n    s: date.getSeconds(),\n    a: date.getDay()\n  }\n  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {\n    let value = formatObj[key]\n    // Note: getDay() returns 0 on Sunday\n    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }\n    if (result.length > 0 && value < 10) {\n      value = '0' + value\n    }\n    return value || 0\n  })\n  return time_str\n}\n\n// 表单重置\nexport function resetForm(refName) {\n  if (this.$refs[refName]) {\n    this.$refs[refName].resetFields();\n  }\n}\n\n// 添加日期范围\nexport function addDateRange(params, dateRange, propNameS,propNameE) {\n  let search = params;\n  search.params = typeof (search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};\n  dateRange = Array.isArray(dateRange) ? dateRange : [];\n  if (typeof (propNameS) === 'undefined'&&typeof (propNameE) === 'undefined') {\n    search.params['beginTime'] = dateRange[0];\n    search.params['endTime'] = dateRange[1];\n  } else {\n    search[propNameS] = dateRange[0];\n    search[propNameE] = dateRange[1];\n  }\n  return search;\n}\n\n// 回显数据字典\nexport function selectDictLabel(datas, value) {\n  if (value === undefined) {\n    return \"\";\n  }\n  var actions = [];\n  Object.keys(datas).some((key) => {\n    if (datas[key].value == ('' + value)) {\n      actions.push(datas[key].label);\n      return true;\n    }\n  })\n  if (actions.length === 0) {\n    actions.push(value);\n  }\n  return actions.join('');\n}\n\n// 回显数据字典（字符串数组）\nexport function selectDictLabels(datas, value, separator) {\n  if (value === undefined) {\n    return \"\";\n  }\n  var actions = [];\n  var currentSeparator = undefined === separator ? \",\" : separator;\n  var temp = value.split(currentSeparator);\n  Object.keys(value.split(currentSeparator)).some((val) => {\n    var match = false;\n    Object.keys(datas).some((key) => {\n      if (datas[key].value == ('' + temp[val])) {\n        actions.push(datas[key].label + currentSeparator);\n        match = true;\n      }\n    })\n    if (!match) {\n      actions.push(temp[val] + currentSeparator);\n    }\n  })\n  return actions.join('').substring(0, actions.join('').length - 1);\n}\n\n// 字符串格式化(%s )\nexport function sprintf(str) {\n  var args = arguments, flag = true, i = 1;\n  str = str.replace(/%s/g, function () {\n    var arg = args[i++];\n    if (typeof arg === 'undefined') {\n      flag = false;\n      return '';\n    }\n    return arg;\n  });\n  return flag ? str : '';\n}\n\n// 转换字符串，undefined,null等转化为\"\"\nexport function parseStrEmpty(str) {\n  if (!str || str == \"undefined\" || str == \"null\") {\n    return \"\";\n  }\n  return str;\n}\n\n// 数据合并\nexport function mergeRecursive(source, target) {\n  for (var p in target) {\n    try {\n      if (target[p].constructor == Object) {\n        source[p] = mergeRecursive(source[p], target[p]);\n      } else {\n        source[p] = target[p];\n      }\n    } catch (e) {\n      source[p] = target[p];\n    }\n  }\n  return source;\n};\n\n/**\n * 构造树型结构数据\n * @param {*} data 数据源\n * @param {*} id id字段 默认 'id'\n * @param {*} parentId 父节点字段 默认 'parentId'\n * @param {*} children 孩子节点字段 默认 'children'\n */\nexport function handleTree(data, id, parentId, children) {\n  let config = {\n    id: id || 'id',\n    parentId: parentId || 'parentId',\n    childrenList: children || 'children'\n  };\n\n  var childrenListMap = {};\n  var nodeIds = {};\n  var tree = [];\n\n  for (let d of data) {\n    let parentId = d[config.parentId];\n    if (childrenListMap[parentId] == null) {\n      childrenListMap[parentId] = [];\n    }\n    nodeIds[d[config.id]] = d;\n    childrenListMap[parentId].push(d);\n  }\n\n  for (let d of data) {\n    let parentId = d[config.parentId];\n    if (nodeIds[parentId] == null) {\n      tree.push(d);\n    }\n  }\n\n  for (let t of tree) {\n    adaptToChildrenList(t);\n  }\n\n  function adaptToChildrenList(o) {\n    if (childrenListMap[o[config.id]] !== null) {\n      o[config.childrenList] = childrenListMap[o[config.id]];\n    }\n    if (o[config.childrenList]) {\n      for (let c of o[config.childrenList]) {\n        adaptToChildrenList(c);\n      }\n    }\n  }\n  return tree;\n}\n\n/**\n* 参数处理\n* @param {*} params  参数\n*/\nexport function tansParams(params) {\n  let result = ''\n  for (const propName of Object.keys(params)) {\n    const value = params[propName];\n    var part = encodeURIComponent(propName) + \"=\";\n    if (value !== null && typeof (value) !== \"undefined\") {\n      if (typeof value === 'object') {\n        for (const key of Object.keys(value)) {\n          if (value[key] !== null && typeof (value[key]) !== 'undefined') {\n            let params = propName + '[' + key + ']';\n            var subPart = encodeURIComponent(params) + \"=\";\n            result += subPart + encodeURIComponent(value[key]) + \"&\";\n          }\n        }\n      } else {\n        result += part + encodeURIComponent(value) + \"&\";\n      }\n    }\n  }\n  return result\n}\n\n// 验证是否为blob格式\nexport async function blobValidate(data) {\n  try {\n    const text = await data.text();\n    JSON.parse(text);\n    return false;\n  } catch (error) {\n    return true;\n  }\n}\n// table 列 权限显示\nexport function columnHasPermi(val) {\n  const  value  = val\n  const all_permission = \"*:*:*\";\n  const permissions = store.getters && store.getters.permissions\n  if (value && value instanceof Array && value.length > 0) {\n    const permissionFlag = value\n\n    const hasPermissions = permissions.some(permission => {\n      return all_permission === permission || permissionFlag.includes(permission)\n    })\n\n    if (!hasPermissions) {\n      return false\n    }\n    return true\n  } else {\n    throw new Error(`请设置操作权限标签值`)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAGA;AACA;AACA;AACA;;AAEA;AACO,SAASC,SAASA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACvC,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,IAAI,CAACH,IAAI,EAAE;IACnC,OAAO,IAAI;EACb;EACA,IAAMI,MAAM,GAAGH,OAAO,IAAI,yBAAyB;EACnD,IAAII,IAAI;EACR,IAAI,IAAAC,QAAA,CAAAC,OAAA,EAAOP,IAAI,MAAK,QAAQ,EAAE;IAC5BK,IAAI,GAAGL,IAAI;EACb,CAAC,MAAM;IACL,IAAK,OAAOA,IAAI,KAAK,QAAQ,IAAM,UAAU,CAACQ,IAAI,CAACR,IAAI,CAAE,EAAE;MACzDA,IAAI,GAAGS,QAAQ,CAACT,IAAI,CAAC;IACvB,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACnCA,IAAI,GAAGA,IAAI,CAACU,OAAO,CAAC,IAAIC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAACD,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAIC,MAAM,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;IACtG;IACA,IAAK,OAAOX,IAAI,KAAK,QAAQ,IAAMA,IAAI,CAACY,QAAQ,CAAC,CAAC,CAACT,MAAM,KAAK,EAAG,EAAE;MACjEH,IAAI,GAAGA,IAAI,GAAG,IAAI;IACpB;IACAK,IAAI,GAAG,IAAIQ,IAAI,CAACb,IAAI,CAAC;EACvB;EACA,IAAMc,SAAS,GAAG;IAChBC,CAAC,EAAEV,IAAI,CAACW,WAAW,CAAC,CAAC;IACrBC,CAAC,EAAEZ,IAAI,CAACa,QAAQ,CAAC,CAAC,GAAG,CAAC;IACtBC,CAAC,EAAEd,IAAI,CAACe,OAAO,CAAC,CAAC;IACjBC,CAAC,EAAEhB,IAAI,CAACiB,QAAQ,CAAC,CAAC;IAClBC,CAAC,EAAElB,IAAI,CAACmB,UAAU,CAAC,CAAC;IACpBC,CAAC,EAAEpB,IAAI,CAACqB,UAAU,CAAC,CAAC;IACpBC,CAAC,EAAEtB,IAAI,CAACuB,MAAM,CAAC;EACjB,CAAC;EACD,IAAMC,QAAQ,GAAGzB,MAAM,CAACM,OAAO,CAAC,qBAAqB,EAAE,UAACoB,MAAM,EAAEC,GAAG,EAAK;IACtE,IAAIC,KAAK,GAAGlB,SAAS,CAACiB,GAAG,CAAC;IAC1B;IACA,IAAIA,GAAG,KAAK,GAAG,EAAE;MAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACC,KAAK,CAAC;IAAC;IACrE,IAAIF,MAAM,CAAC3B,MAAM,GAAG,CAAC,IAAI6B,KAAK,GAAG,EAAE,EAAE;MACnCA,KAAK,GAAG,GAAG,GAAGA,KAAK;IACrB;IACA,OAAOA,KAAK,IAAI,CAAC;EACnB,CAAC,CAAC;EACF,OAAOH,QAAQ;AACjB;;AAEA;AACO,SAASI,SAASA,CAACC,OAAO,EAAE;EACjC,IAAI,IAAI,CAACC,KAAK,CAACD,OAAO,CAAC,EAAE;IACvB,IAAI,CAACC,KAAK,CAACD,OAAO,CAAC,CAACE,WAAW,CAAC,CAAC;EACnC;AACF;;AAEA;AACO,SAASC,YAAYA,CAACC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAACC,SAAS,EAAE;EACnE,IAAIC,MAAM,GAAGJ,MAAM;EACnBI,MAAM,CAACJ,MAAM,GAAG,IAAAhC,QAAA,CAAAC,OAAA,EAAQmC,MAAM,CAACJ,MAAM,MAAM,QAAQ,IAAII,MAAM,CAACJ,MAAM,KAAK,IAAI,IAAI,CAACK,KAAK,CAACC,OAAO,CAACF,MAAM,CAACJ,MAAM,CAAC,GAAGI,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC;EACnIC,SAAS,GAAGI,KAAK,CAACC,OAAO,CAACL,SAAS,CAAC,GAAGA,SAAS,GAAG,EAAE;EACrD,IAAI,OAAQC,SAAU,KAAK,WAAW,IAAE,OAAQC,SAAU,KAAK,WAAW,EAAE;IAC1EC,MAAM,CAACJ,MAAM,CAAC,WAAW,CAAC,GAAGC,SAAS,CAAC,CAAC,CAAC;IACzCG,MAAM,CAACJ,MAAM,CAAC,SAAS,CAAC,GAAGC,SAAS,CAAC,CAAC,CAAC;EACzC,CAAC,MAAM;IACLG,MAAM,CAACF,SAAS,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC;IAChCG,MAAM,CAACD,SAAS,CAAC,GAAGF,SAAS,CAAC,CAAC,CAAC;EAClC;EACA,OAAOG,MAAM;AACf;;AAEA;AACO,SAASG,eAAeA,CAACC,KAAK,EAAEd,KAAK,EAAE;EAC5C,IAAIA,KAAK,KAAKe,SAAS,EAAE;IACvB,OAAO,EAAE;EACX;EACA,IAAIC,OAAO,GAAG,EAAE;EAChBC,MAAM,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,IAAI,CAAC,UAACpB,GAAG,EAAK;IAC/B,IAAIe,KAAK,CAACf,GAAG,CAAC,CAACC,KAAK,IAAK,EAAE,GAAGA,KAAM,EAAE;MACpCgB,OAAO,CAACI,IAAI,CAACN,KAAK,CAACf,GAAG,CAAC,CAACsB,KAAK,CAAC;MAC9B,OAAO,IAAI;IACb;EACF,CAAC,CAAC;EACF,IAAIL,OAAO,CAAC7C,MAAM,KAAK,CAAC,EAAE;IACxB6C,OAAO,CAACI,IAAI,CAACpB,KAAK,CAAC;EACrB;EACA,OAAOgB,OAAO,CAACM,IAAI,CAAC,EAAE,CAAC;AACzB;;AAEA;AACO,SAASC,gBAAgBA,CAACT,KAAK,EAAEd,KAAK,EAAEwB,SAAS,EAAE;EACxD,IAAIxB,KAAK,KAAKe,SAAS,EAAE;IACvB,OAAO,EAAE;EACX;EACA,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIS,gBAAgB,GAAGV,SAAS,KAAKS,SAAS,GAAG,GAAG,GAAGA,SAAS;EAChE,IAAIE,IAAI,GAAG1B,KAAK,CAAC2B,KAAK,CAACF,gBAAgB,CAAC;EACxCR,MAAM,CAACC,IAAI,CAAClB,KAAK,CAAC2B,KAAK,CAACF,gBAAgB,CAAC,CAAC,CAACN,IAAI,CAAC,UAACS,GAAG,EAAK;IACvD,IAAIC,KAAK,GAAG,KAAK;IACjBZ,MAAM,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,IAAI,CAAC,UAACpB,GAAG,EAAK;MAC/B,IAAIe,KAAK,CAACf,GAAG,CAAC,CAACC,KAAK,IAAK,EAAE,GAAG0B,IAAI,CAACE,GAAG,CAAE,EAAE;QACxCZ,OAAO,CAACI,IAAI,CAACN,KAAK,CAACf,GAAG,CAAC,CAACsB,KAAK,GAAGI,gBAAgB,CAAC;QACjDI,KAAK,GAAG,IAAI;MACd;IACF,CAAC,CAAC;IACF,IAAI,CAACA,KAAK,EAAE;MACVb,OAAO,CAACI,IAAI,CAACM,IAAI,CAACE,GAAG,CAAC,GAAGH,gBAAgB,CAAC;IAC5C;EACF,CAAC,CAAC;EACF,OAAOT,OAAO,CAACM,IAAI,CAAC,EAAE,CAAC,CAACQ,SAAS,CAAC,CAAC,EAAEd,OAAO,CAACM,IAAI,CAAC,EAAE,CAAC,CAACnD,MAAM,GAAG,CAAC,CAAC;AACnE;;AAEA;AACO,SAAS4D,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAIC,IAAI,GAAG/D,SAAS;IAAEgE,IAAI,GAAG,IAAI;IAAE3C,CAAC,GAAG,CAAC;EACxCyC,GAAG,GAAGA,GAAG,CAACtD,OAAO,CAAC,KAAK,EAAE,YAAY;IACnC,IAAIyD,GAAG,GAAGF,IAAI,CAAC1C,CAAC,EAAE,CAAC;IACnB,IAAI,OAAO4C,GAAG,KAAK,WAAW,EAAE;MAC9BD,IAAI,GAAG,KAAK;MACZ,OAAO,EAAE;IACX;IACA,OAAOC,GAAG;EACZ,CAAC,CAAC;EACF,OAAOD,IAAI,GAAGF,GAAG,GAAG,EAAE;AACxB;;AAEA;AACO,SAASI,aAAaA,CAACJ,GAAG,EAAE;EACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,IAAI,WAAW,IAAIA,GAAG,IAAI,MAAM,EAAE;IAC/C,OAAO,EAAE;EACX;EACA,OAAOA,GAAG;AACZ;;AAEA;AACO,SAASK,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC7C,KAAK,IAAIC,CAAC,IAAID,MAAM,EAAE;IACpB,IAAI;MACF,IAAIA,MAAM,CAACC,CAAC,CAAC,CAACC,WAAW,IAAIxB,MAAM,EAAE;QACnCqB,MAAM,CAACE,CAAC,CAAC,GAAGH,cAAc,CAACC,MAAM,CAACE,CAAC,CAAC,EAAED,MAAM,CAACC,CAAC,CAAC,CAAC;MAClD,CAAC,MAAM;QACLF,MAAM,CAACE,CAAC,CAAC,GAAGD,MAAM,CAACC,CAAC,CAAC;MACvB;IACF,CAAC,CAAC,OAAOE,CAAC,EAAE;MACVJ,MAAM,CAACE,CAAC,CAAC,GAAGD,MAAM,CAACC,CAAC,CAAC;IACvB;EACF;EACA,OAAOF,MAAM;AACf;AAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASK,UAAUA,CAACC,IAAI,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACvD,IAAIC,MAAM,GAAG;IACXH,EAAE,EAAEA,EAAE,IAAI,IAAI;IACdC,QAAQ,EAAEA,QAAQ,IAAI,UAAU;IAChCG,YAAY,EAAEF,QAAQ,IAAI;EAC5B,CAAC;EAED,IAAIG,eAAe,GAAG,CAAC,CAAC;EACxB,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,IAAI,GAAG,EAAE;EAAC,IAAAC,SAAA,OAAAC,2BAAA,CAAA/E,OAAA,EAEAqE,IAAI;IAAAW,KAAA;EAAA;IAAlB,KAAAF,SAAA,CAAA5D,CAAA,MAAA8D,KAAA,GAAAF,SAAA,CAAAG,CAAA,IAAAC,IAAA,GAAoB;MAAA,IAAXtE,CAAC,GAAAoE,KAAA,CAAAvD,KAAA;MACR,IAAI8C,SAAQ,GAAG3D,CAAC,CAAC6D,MAAM,CAACF,QAAQ,CAAC;MACjC,IAAII,eAAe,CAACJ,SAAQ,CAAC,IAAI,IAAI,EAAE;QACrCI,eAAe,CAACJ,SAAQ,CAAC,GAAG,EAAE;MAChC;MACAK,OAAO,CAAChE,CAAC,CAAC6D,MAAM,CAACH,EAAE,CAAC,CAAC,GAAG1D,CAAC;MACzB+D,eAAe,CAACJ,SAAQ,CAAC,CAAC1B,IAAI,CAACjC,CAAC,CAAC;IACnC;EAAC,SAAAuE,GAAA;IAAAL,SAAA,CAAAX,CAAA,CAAAgB,GAAA;EAAA;IAAAL,SAAA,CAAAM,CAAA;EAAA;EAAA,IAAAC,UAAA,OAAAN,2BAAA,CAAA/E,OAAA,EAEaqE,IAAI;IAAAiB,MAAA;EAAA;IAAlB,KAAAD,UAAA,CAAAnE,CAAA,MAAAoE,MAAA,GAAAD,UAAA,CAAAJ,CAAA,IAAAC,IAAA,GAAoB;MAAA,IAAXtE,EAAC,GAAA0E,MAAA,CAAA7D,KAAA;MACR,IAAI8C,UAAQ,GAAG3D,EAAC,CAAC6D,MAAM,CAACF,QAAQ,CAAC;MACjC,IAAIK,OAAO,CAACL,UAAQ,CAAC,IAAI,IAAI,EAAE;QAC7BM,IAAI,CAAChC,IAAI,CAACjC,EAAC,CAAC;MACd;IACF;EAAC,SAAAuE,GAAA;IAAAE,UAAA,CAAAlB,CAAA,CAAAgB,GAAA;EAAA;IAAAE,UAAA,CAAAD,CAAA;EAAA;EAED,SAAAG,EAAA,MAAAC,KAAA,GAAcX,IAAI,EAAAU,EAAA,GAAAC,KAAA,CAAA5F,MAAA,EAAA2F,EAAA,IAAE;IAAf,IAAIE,CAAC,GAAAD,KAAA,CAAAD,EAAA;IACRG,mBAAmB,CAACD,CAAC,CAAC;EACxB;EAEA,SAASC,mBAAmBA,CAACC,CAAC,EAAE;IAC9B,IAAIhB,eAAe,CAACgB,CAAC,CAAClB,MAAM,CAACH,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;MAC1CqB,CAAC,CAAClB,MAAM,CAACC,YAAY,CAAC,GAAGC,eAAe,CAACgB,CAAC,CAAClB,MAAM,CAACH,EAAE,CAAC,CAAC;IACxD;IACA,IAAIqB,CAAC,CAAClB,MAAM,CAACC,YAAY,CAAC,EAAE;MAAA,IAAAkB,UAAA,OAAAb,2BAAA,CAAA/E,OAAA,EACZ2F,CAAC,CAAClB,MAAM,CAACC,YAAY,CAAC;QAAAmB,MAAA;MAAA;QAApC,KAAAD,UAAA,CAAA1E,CAAA,MAAA2E,MAAA,GAAAD,UAAA,CAAAX,CAAA,IAAAC,IAAA,GAAsC;UAAA,IAA7BY,CAAC,GAAAD,MAAA,CAAApE,KAAA;UACRiE,mBAAmB,CAACI,CAAC,CAAC;QACxB;MAAC,SAAAX,GAAA;QAAAS,UAAA,CAAAzB,CAAA,CAAAgB,GAAA;MAAA;QAAAS,UAAA,CAAAR,CAAA;MAAA;IACH;EACF;EACA,OAAOP,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACO,SAASkB,UAAUA,CAAChE,MAAM,EAAE;EACjC,IAAIR,MAAM,GAAG,EAAE;EACf,SAAAyE,GAAA,MAAAC,YAAA,GAAuBvD,MAAM,CAACC,IAAI,CAACZ,MAAM,CAAC,EAAAiE,GAAA,GAAAC,YAAA,CAAArG,MAAA,EAAAoG,GAAA,IAAE;IAAvC,IAAME,QAAQ,GAAAD,YAAA,CAAAD,GAAA;IACjB,IAAMvE,KAAK,GAAGM,MAAM,CAACmE,QAAQ,CAAC;IAC9B,IAAIC,IAAI,GAAGC,kBAAkB,CAACF,QAAQ,CAAC,GAAG,GAAG;IAC7C,IAAIzE,KAAK,KAAK,IAAI,IAAI,OAAQA,KAAM,KAAK,WAAW,EAAE;MACpD,IAAI,IAAA1B,QAAA,CAAAC,OAAA,EAAOyB,KAAK,MAAK,QAAQ,EAAE;QAC7B,SAAA4E,GAAA,MAAAC,aAAA,GAAkB5D,MAAM,CAACC,IAAI,CAAClB,KAAK,CAAC,EAAA4E,GAAA,GAAAC,aAAA,CAAA1G,MAAA,EAAAyG,GAAA,IAAE;UAAjC,IAAM7E,GAAG,GAAA8E,aAAA,CAAAD,GAAA;UACZ,IAAI5E,KAAK,CAACD,GAAG,CAAC,KAAK,IAAI,IAAI,OAAQC,KAAK,CAACD,GAAG,CAAE,KAAK,WAAW,EAAE;YAC9D,IAAIO,OAAM,GAAGmE,QAAQ,GAAG,GAAG,GAAG1E,GAAG,GAAG,GAAG;YACvC,IAAI+E,OAAO,GAAGH,kBAAkB,CAACrE,OAAM,CAAC,GAAG,GAAG;YAC9CR,MAAM,IAAIgF,OAAO,GAAGH,kBAAkB,CAAC3E,KAAK,CAACD,GAAG,CAAC,CAAC,GAAG,GAAG;UAC1D;QACF;MACF,CAAC,MAAM;QACLD,MAAM,IAAI4E,IAAI,GAAGC,kBAAkB,CAAC3E,KAAK,CAAC,GAAG,GAAG;MAClD;IACF;EACF;EACA,OAAOF,MAAM;AACf;;AAEA;AAAA,SACsBiF,YAAYA,CAAAC,EAAA;EAAA,OAAAC,aAAA,CAAAC,KAAA,OAAAhH,SAAA;AAAA,EASlC;AAAA,SAAA+G,cAAA;EAAAA,aAAA,OAAAE,kBAAA,CAAA5G,OAAA,mBAAA6G,oBAAA,CAAA7G,OAAA,IAAA8G,IAAA,CATO,SAAAC,QAA4B1C,IAAI;IAAA,IAAA2C,IAAA;IAAA,WAAAH,oBAAA,CAAA7G,OAAA,IAAAiH,IAAA,UAAAC,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;QAAA;UAAAF,QAAA,CAAAC,IAAA;UAAAD,QAAA,CAAAE,IAAA;UAAA,OAEhBhD,IAAI,CAAC2C,IAAI,CAAC,CAAC;QAAA;UAAxBA,IAAI,GAAAG,QAAA,CAAAG,IAAA;UACVC,IAAI,CAACC,KAAK,CAACR,IAAI,CAAC;UAAC,OAAAG,QAAA,CAAAM,MAAA,WACV,KAAK;QAAA;UAAAN,QAAA,CAAAC,IAAA;UAAAD,QAAA,CAAAO,EAAA,GAAAP,QAAA;UAAA,OAAAA,QAAA,CAAAM,MAAA,WAEL,IAAI;QAAA;QAAA;UAAA,OAAAN,QAAA,CAAAQ,IAAA;MAAA;IAAA,GAAAZ,OAAA;EAAA,CAEd;EAAA,OAAAL,aAAA,CAAAC,KAAA,OAAAhH,SAAA;AAAA;AAEM,SAASiI,cAAcA,CAACvE,GAAG,EAAE;EAClC,IAAO5B,KAAK,GAAI4B,GAAG;EACnB,IAAMwE,cAAc,GAAG,OAAO;EAC9B,IAAMC,WAAW,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,WAAW;EAC9D,IAAIrG,KAAK,IAAIA,KAAK,YAAYW,KAAK,IAAIX,KAAK,CAAC7B,MAAM,GAAG,CAAC,EAAE;IACvD,IAAMqI,cAAc,GAAGxG,KAAK;IAE5B,IAAMyG,cAAc,GAAGJ,WAAW,CAAClF,IAAI,CAAC,UAAAuF,UAAU,EAAI;MACpD,OAAON,cAAc,KAAKM,UAAU,IAAIF,cAAc,CAACG,QAAQ,CAACD,UAAU,CAAC;IAC7E,CAAC,CAAC;IAEF,IAAI,CAACD,cAAc,EAAE;MACnB,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,MAAM;IACL,MAAM,IAAIG,KAAK,+DAAa,CAAC;EAC/B;AACF", "ignoreList": []}]}