{"remainingRequest": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\git\\huida-reinsurance\\web\\src\\components\\TimeLine\\timeline.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\git\\huida-reinsurance\\web\\src\\components\\TimeLine\\timeline.vue", "mtime": 1722240766000}, {"path": "D:\\git\\huida-reinsurance\\web\\babel.config.js", "mtime": 1722236071000}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750743148245}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750743145173}, {"path": "D:\\git\\huida-reinsurance\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750743149606}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9naXQvaHVpZGEtcmVpbnN1cmFuY2Uvd2ViL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwp2YXIgX2RlZmluZVByb3BlcnR5MiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovZ2l0L2h1aWRhLXJlaW5zdXJhbmNlL3dlYi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9kZWZpbmVQcm9wZXJ0eS5qcyIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIHByb3BzOiB7CiAgICBsaXN0OiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICBkZWZhdWx0OiBbXQogICAgfSwKICAgIHJpc2tDb2RlOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgX3JlZjsKICAgIHJldHVybiBfcmVmID0gewogICAgICBvZGl2X3Q6IG51bGwsCiAgICAgIG9kaXY6IG51bGwsCiAgICAgIG9kaXZfbGVuZ3RoOiAwCiAgICB9LCAoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KShfcmVmLCAib2Rpdl9sZW5ndGgiLCAwKSwgIm51bSIsIDApLCAid2hlZWwiLCBudWxsKSwgInRpbWVsaW5lX2JveCIsIG51bGwpLCAidGltZWxlbmdodCIsIDApLCAibG4iLCAwKSwgImRhdGFfMSIsIHt9KSwgImRhdGFfMiIsIHt9KSwgImRhdGFfMyIsIHt9KSwgImRhdGFfNCIsIHt9KSwgKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoKDAsIF9kZWZpbmVQcm9wZXJ0eTIuZGVmYXVsdCkoX3JlZiwgImRhdGFfNSIsIHt9KSwgInNob3ciLCBmYWxzZSk7CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5vZGl2X3QgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcudGltZWxpbmVfZGl2Jyk7CiAgICB0aGlzLm9kaXYgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcudGltZWxpbmVfZGl2IGRpdicpOwogICAgdGhpcy50aW1lbGluZV9ib3ggPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcudGltZWxpbmUtYm94Jyk7CiAgICB0aGlzLm9kaXZfbGVuZ3RoID0gdGhpcy5vZGl2Lmxlbmd0aDsKICAgIHRoaXMudGltZWxlbmdodCA9IHRoaXMudGltZWxpbmVfYm94Lmxlbmd0aDsKICB9LAogIG1ldGhvZHM6IHsKICAgIGluaXQ6IGZ1bmN0aW9uIGluaXQoKSB7CiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgdGhpcy5vZGl2X2xlbmd0aDsgaSsrKSB7CiAgICAgICAgaWYgKGkgJSA0ID09IDApIHsKICAgICAgICAgIHRoaXMub2RpdltpXS5jbGFzc05hbWUgPSAnZi1ib3JkZXInOwogICAgICAgIH0KICAgICAgICB2YXIgbiA9IHBhcnNlSW50KGkgLyA0KSAlIDIgPT0gMCA/IHRoaXMubnVtKysgOiB0aGlzLm51bS0tOwogICAgICAgIHRoaXMud2hlZWwgPSB0aGlzLm9kaXZfdC5vZmZzZXRIZWlnaHQgLyA1ICogbjsKICAgICAgICB0aGlzLm9kaXZbaV0uc3R5bGUudHJhbnNmb3JtID0gJ3RyYW5zbGF0ZVkoJyArIHRoaXMud2hlZWwgKyAncHgpJzsKICAgICAgICB0aGlzLmhhbmRsZWNoYW5nZShpLCB0aGlzKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZWNoYW5nZTogZnVuY3Rpb24gaGFuZGxlY2hhbmdlKGksIF90aGlzKSB7CiAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLm9kaXZbX3RoaXMubG5dLmNsYXNzTGlzdC5yZW1vdmUoJ2FjdGl2ZScpOwogICAgICAgIF90aGlzLm9kaXZbaV0uY2xhc3NMaXN0LmFkZCgnYWN0aXZlJyk7CiAgICAgICAgX3RoaXMubG4gPSBpOwogICAgICAgIGlmIChpID09IF90aGlzLm9kaXZfbGVuZ3RoIC0gMSkgewogICAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIF90aGlzLm9kaXZbaV0uY2xhc3NMaXN0LnJlbW92ZSgnYWN0aXZlJyk7CiAgICAgICAgICB9LCA1MDApOwogICAgICAgIH0KICAgICAgICBfdGhpcy50aW1lbGluZV9ib3hbcGFyc2VJbnQoaSAvIDQpXS5zdHlsZS5vcGFjaXR5ID0gMTsKICAgICAgICBfdGhpcy50aW1lbGluZV9ib3hbcGFyc2VJbnQoaSAvIDQpXS5zdHlsZS50cmFuc2Zvcm0gPSAnc2NhbGVYKDEpJzsKICAgICAgfSwgMTUwICogaSk7CiAgICB9LAogICAgbW91c2U6IGZ1bmN0aW9uIG1vdXNlKGkpIHsKICAgICAgdGhpcy4kcmVmc1siZGl2Ii5jb25jYXQoaSldLnN0eWxlLnRyYW5zZm9ybSA9IGkgJSAyID8gJ3RyYW5zbGF0ZVkoMTBweCknIDogJ3RyYW5zbGF0ZVkoLTEwcHgpJzsKICAgICAgdGhpcy4kcmVmc1siaW1nIi5jb25jYXQoaSldLnN0eWxlLnRyYW5zZm9ybSA9ICdyb3RhdGUoMzYwZGVnKSc7CiAgICB9LAogICAgbW91c2VsOiBmdW5jdGlvbiBtb3VzZWwoaSkgewogICAgICB0aGlzLiRyZWZzWyJkaXYiLmNvbmNhdChpKV0uc3R5bGUudHJhbnNmb3JtID0gJ3RyYW5zbGF0ZVkoMCknOwogICAgICB0aGlzLiRyZWZzWyJpbWciLmNvbmNhdChpKV0uc3R5bGUudHJhbnNmb3JtID0gJ3JvdGF0ZSgwKSc7CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgbGlzdDogewogICAgICBkZWVwOiB0cnVlLAogICAgICBpbW1lZGlhdGU6IHRydWUsCiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIoKSB7CiAgICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgICAgdGhpcy5zaG93ID0gZmFsc2U7CiAgICAgICAgaWYgKHRoaXMubGlzdC5sZW5ndGgpIHsKICAgICAgICAgIHRoaXMubnVtID0gMCwgdGhpcy53aGVlbCA9IG51bGwsIHRoaXMubG4gPSAwLCB0aGlzLnNob3cgPSB0cnVlOwogICAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCB0aGlzLm9kaXZfbGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgdGhpcy50aW1lbGluZV9ib3hbcGFyc2VJbnQoaSAvIDQpXS5zdHlsZS5vcGFjaXR5ID0gMDsKICAgICAgICAgICAgdGhpcy50aW1lbGluZV9ib3hbcGFyc2VJbnQoaSAvIDQpXS5zdHlsZS50cmFuc2Zvcm0gPSAnc2NhbGVYKDApJzsKICAgICAgICAgIH0KICAgICAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgICAgICBfdGhpczIub2Rpdl90ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLnRpbWVsaW5lX2RpdicpOwogICAgICAgICAgICBfdGhpczIub2RpdiA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy50aW1lbGluZV9kaXYgZGl2Jyk7CiAgICAgICAgICAgIF90aGlzMi50aW1lbGluZV9ib3ggPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcudGltZWxpbmUtYm94Jyk7CiAgICAgICAgICAgIF90aGlzMi5vZGl2X2xlbmd0aCA9IF90aGlzMi5vZGl2Lmxlbmd0aDsKICAgICAgICAgICAgX3RoaXMyLnRpbWVsZW5naHQgPSBfdGhpczIudGltZWxpbmVfYm94Lmxlbmd0aDsKICAgICAgICAgICAgX3RoaXMyLmRhdGFfMSA9IF90aGlzMi5saXN0WzBdIHx8IHt9OwogICAgICAgICAgICBfdGhpczIuZGF0YV8yID0gX3RoaXMyLmxpc3RbMV0gfHwge307CiAgICAgICAgICAgIF90aGlzMi5kYXRhXzMgPSBfdGhpczIubGlzdFsyXSB8fCB7fTsKICAgICAgICAgICAgX3RoaXMyLmRhdGFfNCA9IF90aGlzMi5saXN0WzNdIHx8IHt9OwogICAgICAgICAgICBfdGhpczIuZGF0YV81ID0gX3RoaXMyLmxpc3RbNF0gfHwge307CiAgICAgICAgICAgIF90aGlzMi4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgIF90aGlzMi5pbml0KCk7CiAgICAgICAgICAgICAgX3RoaXMyLiRmb3JjZVVwZGF0ZSgpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0sIDIwMCk7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["props", "list", "type", "Array", "default", "riskCode", "String", "data", "_ref", "odiv_t", "odiv", "odiv_length", "_defineProperty2", "mounted", "document", "querySelector", "querySelectorAll", "timeline_box", "length", "timelenght", "methods", "init", "i", "className", "n", "parseInt", "num", "wheel", "offsetHeight", "style", "transform", "handlechange", "_this", "setTimeout", "ln", "classList", "remove", "add", "opacity", "mouse", "$refs", "concat", "mousel", "watch", "deep", "immediate", "handler", "_this2", "show", "data_1", "data_2", "data_3", "data_4", "data_5", "$nextTick", "$forceUpdate"], "sources": ["src/components/TimeLine/timeline.vue"], "sourcesContent": ["<template>  \n    <el-card class=\"box-card\">\n        <div id=\"timeline\" >\n            <div class=\"timecenter\" v-if=\"show\">    \n                <div class=\"timeline-box\" ref=\"div0\" @mouseover=\"mouse(0)\" @mouseleave=\"mousel(0)\">\n                    <div class=\"header\"><img ref=\"img0\" src=\"../../assets/data/icon_on1.png\"></div>\n                    <router-link :to=\"{ name: 'RiskLiability', params: {riskCode:riskCode}}\">\n                        <h3><el-link type=\"primary\">险种责任信息配置</el-link></h3>\n                    </router-link>\n                    <p v-if=\"data_1.configStepStatus==1\" class=\"check\"><i class=\"el-icon-check\"></i>配置完成</p>\n                    <p v-if=\"data_1.configStepStatus==2\" class=\"close\"><i class=\"el-icon-close\"></i>无需配置</p>\n                    <p v-if=\"data_1.configStepStatus==3\" class=\"loading\"><i class=\"el-icon-loading\"></i>待配置</p>\n                </div>\n                \n                <div class=\"timeline-box\" ref=\"div1\" @mouseover=\"mouse(1)\" @mouseleave=\"mousel(1)\">\n                    <div class=\"header\"><img ref=\"img1\" src=\"../../assets/data/icon_on2.png\"></div>\n                    <router-link :to=\"{ name: 'RiskLiabilityMapping', params: {riskCode:riskCode}}\">\n                        <h3><el-link type=\"primary\">再保责任映射配置</el-link></h3>\n                    </router-link>\n                    <p v-if=\"data_2.configStepStatus==1\" class=\"check\"><i class=\"el-icon-check\"></i>配置完成</p>\n                    <p v-if=\"data_2.configStepStatus==2\" class=\"close\"><i class=\"el-icon-close\"></i>无需配置</p>\n                    <p v-if=\"data_2.configStepStatus==3\" class=\"loading\"><i class=\"el-icon-loading\"></i>待配置</p>\n                </div>   \n                \n                <div class=\"timeline-box\" ref=\"div2\" @mouseover=\"mouse(2)\" @mouseleave=\"mousel(2)\">\n                    <div class=\"header\"><img ref=\"img2\" src=\"../../assets/data/icon_on3.png\"></div>\n                    <router-link :to=\"{ name: 'Associated', params: {riskCode:riskCode}}\">\n                        <h3><el-link type=\"primary\">算法关联配置</el-link></h3>\n                    </router-link>\n                    <p v-if=\"data_3.configStepStatus==1\" class=\"check\"><i class=\"el-icon-check\"></i>配置完成</p>\n                    <p v-if=\"data_3.configStepStatus==2\" class=\"close\"><i class=\"el-icon-close\"></i>无需配置</p>\n                    <p v-if=\"data_3.configStepStatus==3\" class=\"loading\"><i class=\"el-icon-loading\"></i>待配置</p>\n                </div>\n                \n                <div class=\"timeline-box\" ref=\"div3\" @mouseover=\"mouse(3)\" @mouseleave=\"mousel(3)\">\n                    <div class=\"header\"><img ref=\"img3\" src=\"../../assets/data/icon_on4.png\"></div>\n                    <router-link :to=\"{ path: '/cedeoutInfo/contract'}\">\n                        <h3><el-link type=\"primary\">原始合同配置</el-link></h3>\n                    </router-link>\n                    <p v-if=\"data_4.configStepStatus==1\" class=\"check\"><i class=\"el-icon-check\"></i>配置完成</p>\n                    <p v-if=\"data_4.configStepStatus==2\" class=\"close\"><i class=\"el-icon-close\"></i>无需配置</p>\n                    <p v-if=\"data_4.configStepStatus==3\" class=\"loading\"><i class=\"el-icon-loading\"></i>待配置</p>\n                </div>\n                <div class=\"timeline-box\" ref=\"div4\" @mouseover=\"mouse(4)\" @mouseleave=\"mousel(4)\">\n                    <div class=\"header\"><img ref=\"img4\" src=\"../../assets/data/icon_on5.png\"></div>\n                    <router-link :to=\"{ path: '/cedeoutInfo/programme'}\">\n                        <h3><el-link type=\"primary\">再保方案配置</el-link></h3>\n                    </router-link>\n                    <p v-if=\"data_5.configStepStatus==1\" class=\"check\"><i class=\"el-icon-check\"></i>配置完成</p>\n                    <p v-if=\"data_5.configStepStatus==2\" class=\"close\"><i class=\"el-icon-close\"></i>无需配置</p>\n                    <p v-if=\"data_5.configStepStatus==3\" class=\"loading\"><i class=\"el-icon-loading\"></i>待配置</p>\n                </div>\n                <div class=\"timeline_div\">\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <!-- <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div>\n                    <div></div> -->\n                </div>\n            </div>\n            <div v-else>\n                <el-empty description=\"暂无配置\"></el-empty>\n            </div>\n        </div>\n    </el-card>\n\n</template>\n\n<script>\nexport default {\n props: {\n   list: {\n      type: Array,\n      default: []\n    },\n    riskCode:{\n        type:String,\n        default:''\n    }\n },\n data() {\n   return {\n     odiv_t:null,\n     odiv:null,\n     odiv_length:0,\n     odiv_length:0,\n     num:0,\n     wheel:null,\n     timeline_box:null,\n     timelenght:0,\n     ln:0,\n     data_1:{},\n     data_2:{},\n     data_3:{},\n     data_4:{},\n     data_5:{},\n     show:false\n   }\n },\n mounted () {\n   this.odiv_t = document.querySelector('.timeline_div')\n   this.odiv = document.querySelectorAll('.timeline_div div')\n   this.timeline_box = document.querySelectorAll('.timeline-box')\n   this.odiv_length =this.odiv.length\n   this.timelenght =this.timeline_box.length\n },\n methods: {\n  init(){\n        for(var i=0;i<this.odiv_length;i++){\n            if(i%4==0){\n                this.odiv[i].className='f-border';\n            }\n            var n=parseInt( i / 4) % 2==0?this.num++:this.num--\n            this.wheel=this.odiv_t.offsetHeight/5*n\n            this.odiv[i].style.transform='translateY('+this.wheel+'px)';\n            this.handlechange(i,this)\n        }\n   },\n   handlechange(i,_this){\n    setTimeout(function(){\n      _this.odiv[_this.ln].classList.remove('active');\n      _this.odiv[i].classList.add('active'); \n      _this.ln = i;\n      if(i==_this.odiv_length-1){\n        setTimeout(function(){\n          _this.odiv[i].classList.remove('active');\n        },500)\n      }\n      _this.timeline_box[parseInt(i/4)].style.opacity= 1;  \n      _this.timeline_box[parseInt(i/4)].style.transform='scaleX(1)';\n    },150*i)\n   },\n   mouse(i){\n    this.$refs[`div${i}`].style.transform=i%2?'translateY(10px)':'translateY(-10px)'\n    this.$refs[`img${i}`].style.transform='rotate(360deg)' ; \n   },\n   mousel(i){\n    this.$refs[`div${i}`].style.transform='translateY(0)';\n    this.$refs[`img${i}`].style.transform='rotate(0)' ; \n   },\n },\n watch: {\n   list:{\n     deep: true,\n     immediate: true,\n     handler(){\n        this.show = false \n        if(this.list.length){\n            this.num=0,\n            this.wheel=null,\n            this.ln=0,\n            this.show = true\n            for(var i=0;i<this.odiv_length;i++){\n                this.timeline_box[parseInt(i/4)].style.opacity= 0;  \n                this.timeline_box[parseInt(i/4)].style.transform='scaleX(0)';\n            }\n            setTimeout(()=>{\n                this.odiv_t = document.querySelector('.timeline_div')\n                this.odiv = document.querySelectorAll('.timeline_div div')\n                this.timeline_box = document.querySelectorAll('.timeline-box')\n                this.odiv_length =this.odiv.length\n                this.timelenght =this.timeline_box.length\n                this.data_1 = this.list[0]||{}\n                this.data_2 = this.list[1]||{}\n                this.data_3 = this.list[2]||{}\n                this.data_4 = this.list[3]||{}\n                this.data_5 = this.list[4]||{}\n                this.$nextTick(() => {\n                    this.init()\n                    this.$forceUpdate()\n                })\n            },200)  \n            \n        }\n    }\n   }\n }\n}\n</script>\n\n<style scoped >\nh3,p{ margin: 0; padding: 0;}\nbody{padding: 0; margin: 0;}\nh3{text-align: center;}\nh3 .el-link{font-size: 18px; font-weight: normal; margin: 15px 0 10px; padding-top: 35px;  font-family: \"Hiragino Sans GB\";}\np{margin-bottom: 5px;  color: #999; text-align: center;}\n#timeline{\n    width: 1200px;\n    margin: 50px auto;\n\n}\n.timecenter{\n    position: relative;\n    display: inline-block;\n    height: 600px;\n    width: 100%;\n}\n.timeline-box{\n   width: 160px;\n   height: 160px;\n   box-shadow: 0 0 5px 2px rgba(135,135,135, 0.15);\n   position: absolute;\n   left: 0;\n   right: 0;\n   background-color: #FFF;\n   z-index: 1;\n   border-radius: 10px;\n   opacity: 0;\n   transition: 0.25s;\n  \n   transform: scaleX(0);\n}\n.timeline-box:nth-child(odd){\n    top: 65px;\n}\n.timeline-box:nth-child(even){\n    bottom: 65px;\n}\n\n.timeline-box::before{\n    content: \"\";\n    position: absolute;\n    top: 0;\n    left: 0;\n    bottom: 0;\n    right: 0;\n    background: #FFF;\n      border-radius: 10px;\n    z-index: -1;\n}\n.timeline-box::after{\n    content: \"\";\n    width: 15px;\n    height:15px;\n    position: absolute;\n\n    left: 50%;\n    background-color: #FFF;\n    transform:translateX(-50%)rotate(45deg);\n    box-shadow: 0 0 5px 2px rgba(135,135,135, 0.15);\n    z-index: -2;\n\n}\n\n\n\n\n.timeline-box:nth-child(odd)::after{\n    bottom:-7.5px;\n}\n.timeline-box:nth-child(even)::after{\n    top:-7.5px;\n}\n.timeline-box:nth-child(1)\n{\n    left: 0px;\n}\n.timeline-box:nth-child(2)\n{\n    left: 260px;\n}\n.timeline-box:nth-child(3)\n{\n    left: 520px;\n}\n.timeline-box:nth-child(4)\n{\n    left: 780px;\n}\n.timeline-box:nth-child(5)\n{\n    left: 1040px;\n}\n\n.header{\n    width: 70px;\n    height: 70px;\n\n    position: absolute;\n    left: 50%;\n   \n    margin-left: -35px;\n   \n    display: flex;\n    justify-content: center;\n    text-anchor: middle;\n    background-color: #FFF;\n    padding:10px;\n    border-radius: 50%;;\n    box-sizing: border-box;\n\n}\n\n.timeline-box:nth-child(odd) .header{\n    top: -35px;\n}\n.timeline-box:nth-child(even) .header{\n     bottom: -35px;\n}\n.header::before{\n    content: \"\";\n    position: absolute;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    top: 0;\n    \n    border-radius: 50%;;\n    box-shadow: 0 0 5px 2px rgba(135,135,135, 0.15);\n    z-index: -2;\n}\n.header i{\n    width: 100%;\n    height: 100%;\n    border-radius: 50%;\n    transition: 0.5s;\n    display: block;\n}\n\n\n.timeline_div{\n    width: 1050px; \n    height: 90px; \n    position: absolute;\n\n\n    position: absolute;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    top: 0;\n    \n    margin: auto;\n    display: flex;\n    justify-content: space-between;\n    \n}\n.timeline_div div{\n    width: 10px;\n    height: 10px;\n    border-radius: 50%;\n    background: #ffae12;\n\n}\n.f-border{\n    border-color: #fff;\n    box-shadow: 0 0 8px rgba(0, 0, 0, .2);\n\n}\n.active{\n   border:5px;\n   border-color: #fff;\n   box-shadow: 0 0 10px 3px #ffae12;\n}\n.check{\n    color: #38b03f;\n    font-size: 18px;\n}\n.check i{\n    font-size: 16px;\n}\n.close {\n    font-size: 18px;\n}\n.close i{\n    font-size: 16px;\n}\n\n.loading{\n    color: #ea644a;\n    font-size: 18px;\n}\n.loading i{\n    font-size: 16px;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCA6FA;EACAA,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IAAA,IAAAC,IAAA;IACA,OAAAA,IAAA;MACAC,MAAA;MACAC,IAAA;MACAC,WAAA;IAAA,OAAAC,gBAAA,CAAAR,OAAA,MAAAQ,gBAAA,CAAAR,OAAA,MAAAQ,gBAAA,CAAAR,OAAA,MAAAQ,gBAAA,CAAAR,OAAA,MAAAQ,gBAAA,CAAAR,OAAA,MAAAQ,gBAAA,CAAAR,OAAA,MAAAQ,gBAAA,CAAAR,OAAA,MAAAQ,gBAAA,CAAAR,OAAA,MAAAQ,gBAAA,CAAAR,OAAA,MAAAQ,gBAAA,CAAAR,OAAA,EAAAI,IAAA,iBACA,WACA,aACA,uBACA,qBACA,UACA,cACA,eACA,eACA,eACA,SAAAI,gBAAA,CAAAR,OAAA,MAAAQ,gBAAA,CAAAR,OAAA,EAAAI,IAAA,YACA,aACA;EAEA;EACAK,OAAA,WAAAA,QAAA;IACA,KAAAJ,MAAA,GAAAK,QAAA,CAAAC,aAAA;IACA,KAAAL,IAAA,GAAAI,QAAA,CAAAE,gBAAA;IACA,KAAAC,YAAA,GAAAH,QAAA,CAAAE,gBAAA;IACA,KAAAL,WAAA,QAAAD,IAAA,CAAAQ,MAAA;IACA,KAAAC,UAAA,QAAAF,YAAA,CAAAC,MAAA;EACA;EACAE,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,QAAAX,WAAA,EAAAW,CAAA;QACA,IAAAA,CAAA;UACA,KAAAZ,IAAA,CAAAY,CAAA,EAAAC,SAAA;QACA;QACA,IAAAC,CAAA,GAAAC,QAAA,CAAAH,CAAA,sBAAAI,GAAA,UAAAA,GAAA;QACA,KAAAC,KAAA,QAAAlB,MAAA,CAAAmB,YAAA,OAAAJ,CAAA;QACA,KAAAd,IAAA,CAAAY,CAAA,EAAAO,KAAA,CAAAC,SAAA,wBAAAH,KAAA;QACA,KAAAI,YAAA,CAAAT,CAAA;MACA;IACA;IACAS,YAAA,WAAAA,aAAAT,CAAA,EAAAU,KAAA;MACAC,UAAA;QACAD,KAAA,CAAAtB,IAAA,CAAAsB,KAAA,CAAAE,EAAA,EAAAC,SAAA,CAAAC,MAAA;QACAJ,KAAA,CAAAtB,IAAA,CAAAY,CAAA,EAAAa,SAAA,CAAAE,GAAA;QACAL,KAAA,CAAAE,EAAA,GAAAZ,CAAA;QACA,IAAAA,CAAA,IAAAU,KAAA,CAAArB,WAAA;UACAsB,UAAA;YACAD,KAAA,CAAAtB,IAAA,CAAAY,CAAA,EAAAa,SAAA,CAAAC,MAAA;UACA;QACA;QACAJ,KAAA,CAAAf,YAAA,CAAAQ,QAAA,CAAAH,CAAA,OAAAO,KAAA,CAAAS,OAAA;QACAN,KAAA,CAAAf,YAAA,CAAAQ,QAAA,CAAAH,CAAA,OAAAO,KAAA,CAAAC,SAAA;MACA,SAAAR,CAAA;IACA;IACAiB,KAAA,WAAAA,MAAAjB,CAAA;MACA,KAAAkB,KAAA,OAAAC,MAAA,CAAAnB,CAAA,GAAAO,KAAA,CAAAC,SAAA,GAAAR,CAAA;MACA,KAAAkB,KAAA,OAAAC,MAAA,CAAAnB,CAAA,GAAAO,KAAA,CAAAC,SAAA;IACA;IACAY,MAAA,WAAAA,OAAApB,CAAA;MACA,KAAAkB,KAAA,OAAAC,MAAA,CAAAnB,CAAA,GAAAO,KAAA,CAAAC,SAAA;MACA,KAAAU,KAAA,OAAAC,MAAA,CAAAnB,CAAA,GAAAO,KAAA,CAAAC,SAAA;IACA;EACA;EACAa,KAAA;IACA1C,IAAA;MACA2C,IAAA;MACAC,SAAA;MACAC,OAAA,WAAAA,QAAA;QAAA,IAAAC,MAAA;QACA,KAAAC,IAAA;QACA,SAAA/C,IAAA,CAAAiB,MAAA;UACA,KAAAQ,GAAA,MACA,KAAAC,KAAA,SACA,KAAAO,EAAA,MACA,KAAAc,IAAA;UACA,SAAA1B,CAAA,MAAAA,CAAA,QAAAX,WAAA,EAAAW,CAAA;YACA,KAAAL,YAAA,CAAAQ,QAAA,CAAAH,CAAA,OAAAO,KAAA,CAAAS,OAAA;YACA,KAAArB,YAAA,CAAAQ,QAAA,CAAAH,CAAA,OAAAO,KAAA,CAAAC,SAAA;UACA;UACAG,UAAA;YACAc,MAAA,CAAAtC,MAAA,GAAAK,QAAA,CAAAC,aAAA;YACAgC,MAAA,CAAArC,IAAA,GAAAI,QAAA,CAAAE,gBAAA;YACA+B,MAAA,CAAA9B,YAAA,GAAAH,QAAA,CAAAE,gBAAA;YACA+B,MAAA,CAAApC,WAAA,GAAAoC,MAAA,CAAArC,IAAA,CAAAQ,MAAA;YACA6B,MAAA,CAAA5B,UAAA,GAAA4B,MAAA,CAAA9B,YAAA,CAAAC,MAAA;YACA6B,MAAA,CAAAE,MAAA,GAAAF,MAAA,CAAA9C,IAAA;YACA8C,MAAA,CAAAG,MAAA,GAAAH,MAAA,CAAA9C,IAAA;YACA8C,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAA9C,IAAA;YACA8C,MAAA,CAAAK,MAAA,GAAAL,MAAA,CAAA9C,IAAA;YACA8C,MAAA,CAAAM,MAAA,GAAAN,MAAA,CAAA9C,IAAA;YACA8C,MAAA,CAAAO,SAAA;cACAP,MAAA,CAAA1B,IAAA;cACA0B,MAAA,CAAAQ,YAAA;YACA;UACA;QAEA;MACA;IACA;EACA;AACA", "ignoreList": []}]}