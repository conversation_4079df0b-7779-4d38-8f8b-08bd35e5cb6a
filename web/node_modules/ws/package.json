{"name": "ws", "version": "6.2.3", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "homepage": "https://github.com/websockets/ws", "bugs": "https://github.com/websockets/ws/issues", "repository": "websockets/ws", "author": "<PERSON><PERSON> <PERSON> <<EMAIL>> (http://2x.io)", "license": "MIT", "main": "index.js", "browser": "browser.js", "files": ["browser.js", "index.js", "lib/*.js"], "scripts": {"test": "npm run lint && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yml}\""}, "dependencies": {"async-limiter": "~1.0.0"}, "devDependencies": {"benchmark": "~2.1.4", "bufferutil": "~4.0.0", "coveralls": "~3.0.3", "eslint": "~5.15.0", "eslint-config-prettier": "~4.1.0", "eslint-plugin-prettier": "~3.0.0", "mocha": "~6.0.0", "nyc": "~13.3.0", "prettier": "~1.16.1", "utf-8-validate": "~5.0.0"}}