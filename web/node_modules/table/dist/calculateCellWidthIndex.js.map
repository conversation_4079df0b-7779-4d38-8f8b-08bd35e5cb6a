{"version": 3, "sources": ["../src/calculateCellWidthIndex.js"], "names": ["cells", "map", "value", "Math", "max", "split", "line"], "mappings": ";;;;;;;AAAA;;;;AAEA;;;;;;gCAMgBA,K,IAAU;AACxB,SAAOA,KAAK,CAACC,GAAN,CAAWC,KAAD,IAAW;AAC1B,WAAOC,IAAI,CAACC,GAAL,CACL,GAAGF,KAAK,CAACG,KAAN,CAAY,IAAZ,EAAkBJ,GAAlB,CAAuBK,IAAD,IAAU;AACjC,aAAO,0BAAYA,IAAZ,CAAP;AACD,KAFE,CADE,CAAP;AAKD,GANM,CAAP;AAOD,C", "sourcesContent": ["import stringWidth from 'string-width';\n\n/**\n * Calculates width of each cell contents.\n *\n * @param {string[]} cells\n * @returns {number[]}\n */\nexport default (cells) => {\n  return cells.map((value) => {\n    return Math.max(\n      ...value.split('\\n').map((line) => {\n        return stringWidth(line);\n      })\n    );\n  });\n};\n"], "file": "calculateCellWidthIndex.js"}