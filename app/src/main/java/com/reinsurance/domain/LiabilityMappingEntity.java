package com.reinsurance.domain;

import com.jd.lightning.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 再保责任映射对象 t_liability_mapping
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class LiabilityMappingEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 险种编码 */
    private String riskCode;

    /** 核心责任编码 */
    private String lisLiabilityCode;

    /** 核心责任名称 */
    private String lisLiabilityName;

    /** 再保责任编码 */
    private String rsLiabilityCode;

    /** 再保责任名称 */
    private String rsLiabilityName;

    /** 理赔责任编码 */
    private String clLiabilityCode;

    /** 理赔责任名称 */
    private String clLiabilityName;

    /** 监管责任编码 */
    private String supLiabilityCode;

    /** 监管责任名称 */
    private String supLiabilityName;

    /** 状态（0=有效,1=无效） */
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

}
