package com.reinsurance.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.lightning.common.core.domain.BaseEntity;
import com.jd.lightning.common.utils.DateUtils;
import com.reinsurance.enums.CedeoutEnums;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 保全记录标签表（从v_dws_edor视图中获取数据）
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsEdorLabelEntity extends BaseEntity {
	
	private static final long serialVersionUID = 8817369744674088873L;
	
	/** 业务类型(0=新单,1=续期,2=保全,3=理赔,4=满期,5=失效) */
    private Integer busiType = CedeoutEnums.业务类型_保全.getValue();
    
    /** 数据类型(0=分出,1=摊回) */
    private Integer dataType;
    
    /** 保单类型(1=个险,2=团险) */
    private Integer contType = CedeoutEnums.保单类型_个单.getValue();

    /** 提数日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date getDate = DateUtils.getNowDate();

    /** 提数时间 */
    private String getTime = DateUtils.getTime().substring(11);

    /** 所属账期 */
    private Integer accountPeriod = Integer.valueOf(DateUtils.dateTime().substring(0, 6));

    /** 账单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date accountDate = DateUtils.getNowDate();
    
    /** 自增 */
    private Long id;

    /** 唯一键 */
    private String uniqueKey;

    /** 批次号 */
    private String batchNo;
    
    /** 个单合同号码 */
    private String contNo;

    /** 保全险种号 */
    private String polNo;
    
    /** 保全被保险人客户号 */
    private String insuredNo;
    
    /** 保全受理号 */
    private String edorAcceptNo;
    
    /** 批单号 */
    private String edorNo;

    /** 保全项目编码 */
    private String edorType;
    
    /** 保全申请方式 */
    private String edorAppType;

    /** 保全状态(0=有效) */
    private Integer edorState;

    /** 保全补退金额 */
    private BigDecimal edorGetMoney;

    /** 保全补退利息 */
    private BigDecimal edorGetInterest;

    /** 保全创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorMakeDate;

    /** 保全创建日期 */
    private String edorMakeTime;
    
    /** 保全申请日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorAppDate;
    
    /** 业务发生日 max(edorConfDate, edorValidate) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date busiOccurDate;

    /** 业务发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date busiOccurTime;

    /** 保全生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorValidate;

    /** 保全确认日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date edorConfDate;

    /** 保全确认时间 */
    private String edorConfTime;

    /** 保全投保人 */
    private String edorAppName;
    
    /**edorType=RB（保全回退）时存储为回退的保全受理号*/
    private String edorStandbyFlag1;

    /***/
    private String edorStandbyFlag2;
    
    /**edorType=RB（保全回退）时存储为回退的保全项目编码*/
    private String edorStandbyFlag3;
    
    /***/
    private String edorStandbyFlag4;
    
    /***/
    private String edorStandbyFlag5;
    
    /** 处理状态(0=未处理,1=成功,2=失败,3=不需处理) */
    private Integer handleStatus;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date handleDate;

    /** 错误次数 */
    private Integer handleErrorNum;

    /** 错误原因 */
    private String handleErrorMsg;

    /** 状态(0=有效,1=无效) */
    private Integer status;

    /** 是否删除(0=未删除,1=已删除) */
    private Integer isDel;
}
