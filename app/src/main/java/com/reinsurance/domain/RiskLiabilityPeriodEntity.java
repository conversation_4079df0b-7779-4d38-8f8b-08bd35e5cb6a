package com.reinsurance.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.jd.lightning.common.core.domain.BaseEntity;

/**
 * 险种责任保险期间关系对象 t_risk_liability_period
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class RiskLiabilityPeriodEntity extends BaseEntity
{
	private static final long serialVersionUID = 4160330123991704526L;

	/** 主键自增 */
    private Long id;

    /** 险种编码 */
    private String riskCode;

    /** 险种名称 */
    private String riskName;

    /** 责任编码 */
    private String liabilityCode;

    /** 责任名称 */
    private String liabilityName;

    /** 保险期限类型编码 */
    private String periodTypeCode;

    /** 保险期限类型名称 */
    private String periodTypeName;

}
