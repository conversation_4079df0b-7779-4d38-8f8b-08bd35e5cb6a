package com.reinsurance.dto;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公式项申请对象 t_formula_item_apply
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class FormulaItemApplyDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 计算项编码 */
    @Excel(name = "计算项编码")
    private String itemCode;

    /** 计算项名称 */
    @Excel(name = "计算项名称")
    private String itemName;

    /** 计算项英文名称 */
    @Excel(name = "计算项英文名称")
    private String itemEn;

    /** 计算项解释 */
    @Excel(name = "计算项解释")
    private String itemExplain;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

}
