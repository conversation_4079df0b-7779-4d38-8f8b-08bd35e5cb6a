package com.reinsurance.dto;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 再保合同责任对象 t_cedeout_contract_liability
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutContractLiabilityDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 合同ID */
    private Long contractId;

    /** 合同编码 */
    @Excel(name = "合同编码")
    private String contractCode;

    /** 合同编码 */
    @Excel(name = "合同名称")
    private String contractName;

    /** 险种编码 */
    @Excel(name = "险种编码")
    private String riskCode;

    /** 责任编码 */
    @Excel(name = "责任编码")
    private String liabilityCode;

    /** 匹配开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "匹配开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 匹配结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "匹配结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 分保方式 */
    @Excel(name = "分保方式")
    private Integer cedeoutWay;

    /** 自留额 */
    @Excel(name = "自留额")
    private BigDecimal selfAmount;

    /** 自留比例 */
    @Excel(name = "自留比例")
    private BigDecimal selfRatio;

    /** 分出比例 */
    @Excel(name = "分出比例")
    private BigDecimal cedeoutRatio;

    /** 自留额线 */
    @Excel(name = "自留额线")
    private String retentionLine;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

}
