package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutCompanyApplyEntity;
import com.reinsurance.query.CedeoutCompanyApplyQuery;

/**
 * 再保公司申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public interface CedeoutCompanyApplyMapper 
{
    /**
     * 查询再保公司申请
     * 
     * @param id 再保公司申请主键
     * @return 再保公司申请
     */
    public CedeoutCompanyApplyEntity selectCedeoutCompanyApplyById(Long id);

    /**
     * 查询再保公司申请列表
     * 
     * @param cedeoutCompanyApplyQuery 再保公司申请
     * @return 再保公司申请集合
     */
    public List<CedeoutCompanyApplyEntity> selectCedeoutCompanyApplyList(CedeoutCompanyApplyQuery cedeoutCompanyApplyQuery);

    /**
     * 新增再保公司申请
     * 
     * @param cedeoutCompanyApply 再保公司申请
     * @return 结果
     */
    public int insertCedeoutCompanyApply(CedeoutCompanyApplyEntity cedeoutCompanyApply);

    /**
     * 修改再保公司申请
     * 
     * @param cedeoutCompanyApply 再保公司申请
     * @return 结果
     */
    public int updateCedeoutCompanyApply(CedeoutCompanyApplyEntity cedeoutCompanyApply);

    /**
     * 删除再保公司申请
     * 
     * @param id 再保公司申请主键
     * @return 结果
     */
    public int deleteCedeoutCompanyApplyById(Long id);

    /**
     * 批量删除再保公司申请
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutCompanyApplyByIds(Long[] ids);
}
