package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutProgrammeTrackEntity;
import com.reinsurance.query.CedeoutProgrammeTrackQuery;

/**
 * 再保分出方案轨迹Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface CedeoutProgrammeTrackMapper 
{
    /**
     * 查询再保分出方案轨迹
     * 
     * @param trackId 再保分出方案轨迹主键
     * @return 再保分出方案轨迹
     */
    public CedeoutProgrammeTrackEntity selectCedeoutProgrammeTrackByTrackId(Long trackId);

    /**
     * 查询再保分出方案轨迹列表
     * 
     * @param cedeoutProgrammeTrackQuery 再保分出方案轨迹
     * @return 再保分出方案轨迹集合
     */
    public List<CedeoutProgrammeTrackEntity> selectCedeoutProgrammeTrackList(CedeoutProgrammeTrackQuery cedeoutProgrammeTrackQuery);

    /**
     * 新增再保分出方案轨迹
     * 
     * @param cedeoutProgrammeTrack 再保分出方案轨迹
     * @return 结果
     */
    public int insertCedeoutProgrammeTrack(CedeoutProgrammeTrackEntity cedeoutProgrammeTrack);

    /**
     * 修改再保分出方案轨迹
     * 
     * @param cedeoutProgrammeTrack 再保分出方案轨迹
     * @return 结果
     */
    public int updateCedeoutProgrammeTrack(CedeoutProgrammeTrackEntity cedeoutProgrammeTrack);

    /**
     * 删除再保分出方案轨迹
     * 
     * @param trackId 再保分出方案轨迹主键
     * @return 结果
     */
    public int deleteCedeoutProgrammeTrackByTrackId(Long trackId);

    /**
     * 批量删除再保分出方案轨迹
     * 
     * @param trackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutProgrammeTrackByTrackIds(Long[] trackIds);
}
