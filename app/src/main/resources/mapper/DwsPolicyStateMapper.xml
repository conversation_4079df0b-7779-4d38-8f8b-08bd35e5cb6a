<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsPolicyStateMapper">

	<resultMap type="DwsPolicyStateEntity" id="DwsPolicyStateResult">
		<result property="uniqueKey"    column="unique_key"    />
		<result property="contNo" column="cont_no" />
		<result property="polNo" column="pol_no" />
		<result property="insuredNo" column="insured_no" />
		<result property="stateType" column="state_type" />
		<result property="stateState" column="state_state" />
		<result property="stateReason" column="state_reason" />
		<result property="busiOccurTime"    column="busi_occur_time"    />
        <result property="busiOccurDate"    column="busi_occur_date"    />
		<result property="stateStartDate" column="state_start_date" />
		<result property="stateEndDate" column="state_end_date" />
		<result property="stateMakeDate" column="state_make_date" />
		<result property="stateMakeTime" column="state_make_time" />
	</resultMap>

	<sql id="selectDwsPolicyStateVo">
		select unique_key, cont_no, pol_no, insured_no, state_type, state_state, state_reason, busi_occur_time, busi_occur_date, state_start_date, state_end_date, state_make_date, state_make_time from v_dws_policy_state
	</sql>

	<select id="selectDwsPolicyStateList" parameterType="DwsPolicyStateEntity" resultMap="DwsPolicyStateResult">
		<include refid="selectDwsPolicyStateVo" />
		<where>
			<if test="uniqueKey != null  and uniqueKey != ''"> and unique_key = #{uniqueKey}</if>
			<if test="contNo != null  and contNo != ''"> and cont_no = #{contNo}</if>
			<if test="polNo != null  and polNo != ''"> and pol_no = #{polNo}</if>
			<if test="insuredNo != null  and insuredNo != ''"> and insured_no = #{insuredNo}</if>
			<if test="stateType != null  and stateType != ''"> and state_type = #{stateType}</if>
			<if test="stateState != null  and stateState != ''"> and state_state = #{stateState}</if>
			<if test="stateReason != null  and stateReason != ''"> and state_reason = #{stateReason}</if>
			<if test="busiOccurTime != null"> and busi_occur_time = #{busiOccurTime}</if>
			<if test="busiOccurDate != null"> and busi_occur_date = #{busiOccurDate}</if>
			<if test="stateStartDate != null"> and state_start_date = #{stateStartDate}</if>
			<if test="stateEndDate != null"> and state_end_date = #{stateEndDate}</if>
			<if test="stateMakeDate != null"> and state_make_date = #{stateMakeDate}</if>
			<if test="stateMakeTime != null and stateMakeTime != ''"> and state_make_time = #{stateMakeTime}</if>
			<if test="params.startDate != null"> 
				and state_start_date &gt;= date_format(#{params.startDate}, '%Y-%m-%d')
			</if>
			<if test="params.endDate != null"> 
				and state_start_date &lt;= date_format(#{params.endDate}, '%Y-%m-%d')
			</if>
			<if test="params.invalidNotExpired != null"> 
				and state_reason != #{params.invalidNotExpired}
			</if>
		</where>
	</select>
</mapper>