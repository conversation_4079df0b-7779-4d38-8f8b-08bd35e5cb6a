<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.ImportDataLogMapper">
    
    <resultMap type="ImportDataLogEntity" id="ImportDataLogResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="dataStatus"    column="data_status"    />
        <result property="importName"    column="import_name"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectImportDataLogVo">
        select id, batch_no, data_status, import_name, effective_date, remark, is_del, create_by, create_time, update_by, update_time from t_import_data_log
    </sql>

    <select id="selectImportDataLogList" parameterType="ImportDataLogQuery" resultMap="ImportDataLogResult">
        <include refid="selectImportDataLogVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="dataStatus != null "> and data_status = #{dataStatus}</if>
            <if test="importName != null  and importName != ''"> and import_name like concat('%', #{importName}, '%')</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
                <![CDATA[
                    and create_time >= #{beginTime}
                    and create_time <= #{endTime}
                ]]>
            </if>
        </where>
    </select>
    
    <select id="selectImportDataLogById" parameterType="Long" resultMap="ImportDataLogResult">
        <include refid="selectImportDataLogVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertImportDataLog" parameterType="ImportDataLogEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_import_data_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="dataStatus != null">data_status,</if>
            <if test="importName != null and importName != ''">import_name,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="dataStatus != null">#{dataStatus},</if>
            <if test="importName != null and importName != ''">#{importName},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateImportDataLog" parameterType="ImportDataLogEntity">
        update t_import_data_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="dataStatus != null">data_status = #{dataStatus},</if>
            <if test="importName != null and importName != ''">import_name = #{importName},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteImportDataLogById" parameterType="Long">
        delete from t_import_data_log where id = #{id}
    </delete>

    <delete id="deleteImportDataLogByIds" parameterType="String">
        delete from t_import_data_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>